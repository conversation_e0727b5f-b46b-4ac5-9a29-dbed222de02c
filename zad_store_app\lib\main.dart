import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'utils/AppColors.dart';
import 'utils/AppConfig.dart';
import 'services/AuthService.dart';
import 'services/OrderService.dart';
import 'services/NotificationService.dart';
import 'services/BackgroundService.dart';
import 'services/FirebaseNotificationService.dart';
import 'pages/SplashScreen.dart';
import 'pages/LoginPage.dart';
import 'pages/HomePage.dart';
import 'pages/OrdersPage.dart';
import 'pages/DailyReportsPage.dart';
import 'pages/ProfilePage.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة الخدمات الأساسية
  await _initializeServices();

  runApp(const ZadStoreApp());
}

Future<void> _initializeServices() async {
  try {
    // تهيئة Firebase
    final firebaseNotificationService = FirebaseNotificationService();
    await firebaseNotificationService.initialize();
    print('تم تهيئة خدمة Firebase');

    // تهيئة خدمة الإشعارات
    final notificationService = NotificationService();
    await notificationService.initialize();
    print('تم تهيئة خدمة الإشعارات');

    // تهيئة خدمة العمل في الخلفية
    final backgroundService = BackgroundService();
    await backgroundService.initialize();
    print('تم تهيئة خدمة العمل في الخلفية');

    // تهيئة خدمة المصادقة
    final authService = AuthService();
    await authService.initialize();
    print('تم تهيئة خدمة المصادقة');

    print('تم تهيئة جميع الخدمات بنجاح');
  } catch (e) {
    print('خطأ في تهيئة الخدمات: $e');
  }
}

class ZadStoreApp extends StatelessWidget {
  const ZadStoreApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // يمكن إضافة providers هنا إذا لزم الأمر
        Provider<AuthService>(
          create: (_) => AuthService(),
        ),
        Provider<OrderService>(
          create: (_) => OrderService(),
        ),
        Provider<NotificationService>(
          create: (_) => NotificationService(),
        ),
        Provider<FirebaseNotificationService>(
          create: (_) => FirebaseNotificationService(),
        ),
      ],
      child: ScreenUtilInit(
        designSize: const Size(360, 690),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return MaterialApp(
            
            title: AppConfig.appName,
            debugShowCheckedModeBanner: false,
            
            // إعدادات التطبيق
            theme: ThemeData(
              primarySwatch: Colors.red,
              primaryColor: AppColors.primaryColor,
              scaffoldBackgroundColor: AppColors.backgroundColor,
              fontFamily: 'Cairo',
              
              // إعدادات الألوان
              colorScheme: ColorScheme.fromSeed(
                seedColor: AppColors.primaryColor,
                brightness: Brightness.light,
              ),
              
              // إعدادات الأزرار
              elevatedButtonTheme: ElevatedButtonThemeData(
                style: AppButtonStyles.primaryButton,
              ),
              
              // إعدادات حقول الإدخال
              inputDecorationTheme: InputDecorationTheme(
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16.w,
                  vertical: 12.h,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: const BorderSide(color: AppColors.borderColor),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12.r),
                  borderSide: const BorderSide(
                    color: AppColors.primaryColor,
                    width: 2,
                  ),
                ),
                filled: true,
                fillColor: AppColors.backgroundColor,
              ),
              
              // إعدادات البطاقات
              cardTheme: CardTheme(
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.r),
                ),
                color: AppColors.cardColor,
              ),
              
              // إعدادات شريط التطبيق
              appBarTheme: AppBarTheme(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: AppColors.whiteColor,
                elevation: 0,
                centerTitle: true,
                titleTextStyle: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.whiteColor,
                ),
              ),
              
              // إعدادات النصوص
              textTheme: TextTheme(
                displayLarge: AppTextStyles.titleLarge,
                displayMedium: AppTextStyles.titleMedium,
                displaySmall: AppTextStyles.titleSmall,
                bodyLarge: AppTextStyles.bodyLarge,
                bodyMedium: AppTextStyles.bodyMedium,
                bodySmall: AppTextStyles.bodySmall,
              ),
              
              // إعدادات الأيقونات
              iconTheme: const IconThemeData(
                color: AppColors.textPrimary,
              ),
              
              // إعدادات الفواصل
              dividerTheme: const DividerThemeData(
                color: AppColors.dividerColor,
                thickness: 1,
              ),
            ),
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],




            
            // إعدادات اللغة والاتجاه
            locale: const Locale('ar', 'YE'),
            supportedLocales: const [
              Locale('ar', 'YE'),
              Locale('en', 'US'),
            ],
            
            // إعدادات التنقل
            initialRoute: '/splash',
            routes: {
              '/splash': (context) => const SplashScreen(),
              '/login': (context) => const LoginPage(),
              '/home': (context) => const HomePage(),
              '/orders': (context) => const OrdersPage(),
              '/reports': (context) => const DailyReportsPage(),
              '/profile': (context) => const ProfilePage(),
            },
            
            // معالج الأخطاء
            builder: (context, widget) {
              // التأكد من أن النصوص لا تتأثر بشكل مفرط بإعدادات النظام
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaler: TextScaler.linear(
                    MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.3),
                  ),
                ),
                child: Directionality(
                  textDirection: TextDirection.rtl,
                  child: widget!,
                ),
              );
            },
            
            // معالج الأخطاء العامة
            onGenerateRoute: (settings) {
              // معالجة المسارات غير المعرفة
              return MaterialPageRoute(
                builder: (context) => Scaffold(
                  appBar: AppBar(
                    title: const Text('صفحة غير موجودة'),
                  ),
                  body: const Center(
                    child: Text('الصفحة المطلوبة غير موجودة'),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}

// معالج الأخطاء العامة
class ErrorHandler {
  static void handleError(dynamic error, StackTrace stackTrace) {
    print('خطأ في التطبيق: $error');
    print('تفاصيل الخطأ: $stackTrace');
    
    // يمكن إضافة منطق إرسال التقارير هنا
    // مثل Firebase Crashlytics أو Sentry
  }
}

// إعدادات التطبيق العامة
class AppLifecycleHandler extends WidgetsBindingObserver {
  final BackgroundService _backgroundService = BackgroundService();
  final NotificationService _notificationService = NotificationService();

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        print('التطبيق في المقدمة');
        // يمكن إضافة منطق تحديث البيانات هنا
        break;
        
      case AppLifecycleState.paused:
        print('التطبيق في الخلفية');
        // بدء خدمات الخلفية
        _backgroundService.startOrderMonitoring();
        break;
        
      case AppLifecycleState.detached:
        print('التطبيق مغلق');
        break;
        
      case AppLifecycleState.inactive:
        print('التطبيق غير نشط');
        break;
        
      case AppLifecycleState.hidden:
        print('التطبيق مخفي');
        break;
    }
  }
}
