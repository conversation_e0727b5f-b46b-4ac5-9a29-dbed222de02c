{"name": "zad-store-firebase-test", "version": "1.0.0", "description": "Firebase notifications testing for Zad Store App", "main": "test_firebase_notifications.js", "scripts": {"test": "node test_firebase_notifications.js", "add-notification": "node -e \"require('./test_firebase_notifications').addNotificationToFirestore()\"", "send-fcm": "node -e \"require('./test_firebase_notifications').sendFCMNotification()\"", "add-multiple": "node -e \"require('./test_firebase_notifications').addMultipleTestNotifications()\"", "clear-all": "node -e \"require('./test_firebase_notifications').clearAllNotifications()\""}, "dependencies": {"firebase-admin": "^12.0.0"}, "keywords": ["firebase", "notifications", "flutter", "zad-store"], "author": "Zad Store Team", "license": "MIT"}