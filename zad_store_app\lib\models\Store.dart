class Store {
  final String id;
  final String name;
  final String ownerName;
  final String email;
  final String phone;
  final String address;
  final String category;
  final String description;
  final String? logoUrl;
  final double latitude;
  final double longitude;
  final bool isActive;
  final bool isVerified;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? settings;
  final List<String> workingHours;
  final double rating;
  final int totalOrders;
  final double totalRevenue;

  Store({
    required this.id,
    required this.name,
    required this.ownerName,
    required this.email,
    required this.phone,
    required this.address,
    required this.category,
    required this.description,
    this.logoUrl,
    required this.latitude,
    required this.longitude,
    this.isActive = true,
    this.isVerified = false,
    required this.createdAt,
    this.updatedAt,
    this.settings,
    required this.workingHours,
    this.rating = 0.0,
    this.totalOrders = 0,
    this.totalRevenue = 0.0,
  });

  // تحويل من JSON
  factory Store.fromJson(Map<String, dynamic> json) {
    return Store(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      ownerName: json['owner_name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      address: json['address'] ?? '',
      category: json['category'] ?? '',
      description: json['description'] ?? '',
      logoUrl: json['logo_url'],
      latitude: (json['latitude'] ?? 0.0).toDouble(),
      longitude: (json['longitude'] ?? 0.0).toDouble(),
      isActive: json['is_active'] ?? true,
      isVerified: json['is_verified'] ?? false,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      settings: json['settings'],
      workingHours: List<String>.from(json['working_hours'] ?? []),
      rating: (json['rating'] ?? 0.0).toDouble(),
      totalOrders: json['total_orders'] ?? 0,
      totalRevenue: (json['total_revenue'] ?? 0.0).toDouble(),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'owner_name': ownerName,
      'email': email,
      'phone': phone,
      'address': address,
      'category': category,
      'description': description,
      'logo_url': logoUrl,
      'latitude': latitude,
      'longitude': longitude,
      'is_active': isActive,
      'is_verified': isVerified,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'settings': settings,
      'working_hours': workingHours,
      'rating': rating,
      'total_orders': totalOrders,
      'total_revenue': totalRevenue,
    };
  }

  // نسخ مع تعديل
  Store copyWith({
    String? id,
    String? name,
    String? ownerName,
    String? email,
    String? phone,
    String? address,
    String? category,
    String? description,
    String? logoUrl,
    double? latitude,
    double? longitude,
    bool? isActive,
    bool? isVerified,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? settings,
    List<String>? workingHours,
    double? rating,
    int? totalOrders,
    double? totalRevenue,
  }) {
    return Store(
      id: id ?? this.id,
      name: name ?? this.name,
      ownerName: ownerName ?? this.ownerName,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      address: address ?? this.address,
      category: category ?? this.category,
      description: description ?? this.description,
      logoUrl: logoUrl ?? this.logoUrl,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      settings: settings ?? this.settings,
      workingHours: workingHours ?? this.workingHours,
      rating: rating ?? this.rating,
      totalOrders: totalOrders ?? this.totalOrders,
      totalRevenue: totalRevenue ?? this.totalRevenue,
    );
  }

  // التحقق من أن المحل مفتوح حالياً
  bool get isOpenNow {
    if (!isActive) return false;
    
    final now = DateTime.now();
    final currentDay = _getDayName(now.weekday);
    final currentTime = "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}";
    
    for (String schedule in workingHours) {
      if (schedule.contains(currentDay)) {
        // تحليل أوقات العمل (مثال: "الأحد: 08:00-22:00")
        final parts = schedule.split(':');
        if (parts.length >= 2) {
          final timeRange = parts[1].trim();
          final times = timeRange.split('-');
          if (times.length == 2) {
            final openTime = times[0].trim();
            final closeTime = times[1].trim();
            
            if (_isTimeBetween(currentTime, openTime, closeTime)) {
              return true;
            }
          }
        }
      }
    }
    
    return false;
  }

  // الحصول على اسم اليوم
  String _getDayName(int weekday) {
    const days = [
      'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 
      'الجمعة', 'السبت', 'الأحد'
    ];
    return days[weekday - 1];
  }

  // التحقق من أن الوقت بين وقتين
  bool _isTimeBetween(String current, String start, String end) {
    try {
      final currentMinutes = _timeToMinutes(current);
      final startMinutes = _timeToMinutes(start);
      final endMinutes = _timeToMinutes(end);
      
      if (endMinutes < startMinutes) {
        // العمل عبر منتصف الليل
        return currentMinutes >= startMinutes || currentMinutes <= endMinutes;
      } else {
        return currentMinutes >= startMinutes && currentMinutes <= endMinutes;
      }
    } catch (e) {
      return false;
    }
  }

  // تحويل الوقت إلى دقائق
  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  // الحصول على نص حالة المحل
  String get statusText {
    if (!isActive) return 'مغلق مؤقتاً';
    if (!isVerified) return 'في انتظار التحقق';
    if (isOpenNow) return 'مفتوح الآن';
    return 'مغلق';
  }

  @override
  String toString() {
    return 'Store{id: $id, name: $name, ownerName: $ownerName, isActive: $isActive}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Store &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
