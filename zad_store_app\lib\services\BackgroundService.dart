import 'package:workmanager/workmanager.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../utils/AppConfig.dart';
import '../models/Order.dart';
import 'NotificationService.dart';

class BackgroundService {
  static final BackgroundService _instance = BackgroundService._internal();
  factory BackgroundService() => _instance;
  BackgroundService._internal();

  static const String _taskName = AppConfig.backgroundTaskName;
  bool _isInitialized = false;

  // تهيئة خدمة العمل في الخلفية
  Future<void> initialize() async {
    if (_isInitialized) return;

    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: AppConfig.debugMode,
    );

    _isInitialized = true;
    print('تم تهيئة خدمة العمل في الخلفية');
  }

  // بدء مراقبة الطلبات في الخلفية
  Future<void> startOrderMonitoring() async {
    if (!_isInitialized) {
      await initialize();
    }

    await Workmanager().registerPeriodicTask(
      _taskName,
      _taskName,
      frequency: Duration(minutes: AppConfig.backgroundSyncInterval),
      constraints: Constraints(
        networkType: NetworkType.connected,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
      ),
      backoffPolicy: BackoffPolicy.exponential,
      backoffPolicyDelay: Duration(seconds: 30),
    );

    print('تم بدء مراقبة الطلبات في الخلفية');
  }

  // إيقاف مراقبة الطلبات في الخلفية
  Future<void> stopOrderMonitoring() async {
    await Workmanager().cancelByUniqueName(_taskName);
    print('تم إيقاف مراقبة الطلبات في الخلفية');
  }

  // إيقاف جميع المهام
  Future<void> stopAllTasks() async {
    await Workmanager().cancelAll();
    print('تم إيقاف جميع المهام في الخلفية');
  }

  // التحقق من حالة المهام
  Future<bool> isTaskRunning() async {
    // لا توجد طريقة مباشرة للتحقق من حالة المهام في workmanager
    // يمكن استخدام SharedPreferences لتتبع الحالة
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('background_task_running') ?? false;
  }

  // تحديث حالة المهمة
  Future<void> _updateTaskStatus(bool isRunning) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('background_task_running', isRunning);
  }
}

// معالج المهام في الخلفية
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    print('تنفيذ مهمة الخلفية: $task');
    
    try {
      switch (task) {
        case AppConfig.backgroundTaskName:
          await _syncOrdersInBackground();
          break;
        default:
          print('مهمة غير معروفة: $task');
      }
      
      return Future.value(true);
    } catch (e) {
      print('خطأ في تنفيذ مهمة الخلفية: $e');
      return Future.value(false);
    }
  });
}

// مزامنة الطلبات في الخلفية
Future<void> _syncOrdersInBackground() async {
  try {
    // تحميل بيانات المحل والتوكن
    final prefs = await SharedPreferences.getInstance();
    final storeToken = prefs.getString(AppConfig.storeTokenKey);
    final storeDataJson = prefs.getString(AppConfig.storeDataKey);
    
    if (storeToken == null || storeDataJson == null) {
      print('لا توجد بيانات محل محفوظة');
      return;
    }

    final storeData = json.decode(storeDataJson);
    final storeId = storeData['id'];

    // تحميل الطلبات المحفوظة محلياً
    final cachedOrdersJson = prefs.getString(AppConfig.ordersKey);
    List<Order> cachedOrders = [];
    
    if (cachedOrdersJson != null) {
      final List<dynamic> ordersList = json.decode(cachedOrdersJson);
      cachedOrders = ordersList.map((json) => Order.fromJson(json)).toList();
    }

    // جلب الطلبات من الخادم
    final response = await http.get(
      Uri.parse('${AppConfig.getFullUrl(AppConfig.ordersEndpoint)}/$storeId'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $storeToken',
      },
    ).timeout(Duration(seconds: AppConfig.connectionTimeout));

    if (response.statusCode == 200) {
      final List<dynamic> ordersData = json.decode(response.body);
      final List<Order> serverOrders = ordersData.map((json) => Order.fromJson(json)).toList();
      
      // التحقق من الطلبات الجديدة
      await _checkForNewOrdersInBackground(cachedOrders, serverOrders);
      
      // حفظ الطلبات المحدثة
      final updatedOrdersJson = json.encode(serverOrders.map((order) => order.toJson()).toList());
      await prefs.setString(AppConfig.ordersKey, updatedOrdersJson);
      
      print('تم مزامنة ${serverOrders.length} طلب في الخلفية');
    } else {
      print('خطأ في مزامنة الطلبات في الخلفية: ${response.statusCode}');
    }
  } catch (e) {
    print('خطأ في مزامنة الطلبات في الخلفية: $e');
  }
}

// التحقق من الطلبات الجديدة في الخلفية
Future<void> _checkForNewOrdersInBackground(List<Order> cachedOrders, List<Order> serverOrders) async {
  try {
    final existingOrderIds = cachedOrders.map((order) => order.id).toSet();
    final notificationService = NotificationService();
    
    // تهيئة خدمة الإشعارات إذا لم تكن مهيأة
    await notificationService.initialize();
    
    for (Order order in serverOrders) {
      if (!existingOrderIds.contains(order.id) && order.status == 'new') {
        // طلب جديد - إرسال إشعار
        await notificationService.showNewOrderNotification(order);
        print('تم إرسال إشعار طلب جديد في الخلفية: ${order.id}');
      }
    }
  } catch (e) {
    print('خطأ في التحقق من الطلبات الجديدة في الخلفية: $e');
  }
}

// خدمة مراقبة الاتصال
class ConnectionMonitor {
  static final ConnectionMonitor _instance = ConnectionMonitor._internal();
  factory ConnectionMonitor() => _instance;
  ConnectionMonitor._internal();

  bool _isOnline = true;
  Function(bool)? _onConnectionChanged;

  bool get isOnline => _isOnline;

  // تعيين معالج تغيير الاتصال
  void setConnectionChangeHandler(Function(bool) handler) {
    _onConnectionChanged = handler;
  }

  // بدء مراقبة الاتصال
  Future<void> startMonitoring() async {
    // يمكن استخدام connectivity_plus package للمراقبة الفعلية
    // هنا نستخدم محاكاة بسيطة
    
    // محاولة ping للخادم كل دقيقة
    _startPingMonitoring();
  }

  // مراقبة الاتصال عبر ping
  void _startPingMonitoring() {
    // تنفيذ مراقبة الاتصال
    // يمكن تحسينها باستخدام connectivity_plus
  }

  // تحديث حالة الاتصال
  void updateConnectionStatus(bool isOnline) {
    if (_isOnline != isOnline) {
      _isOnline = isOnline;
      _onConnectionChanged?.call(isOnline);
      
      if (isOnline) {
        print('تم استعادة الاتصال بالإنترنت');
        // بدء مزامنة فورية عند استعادة الاتصال
        _syncOrdersInBackground();
      } else {
        print('انقطع الاتصال بالإنترنت');
      }
    }
  }
}

// خدمة إدارة البطارية
class BatteryOptimization {
  static final BatteryOptimization _instance = BatteryOptimization._internal();
  factory BatteryOptimization() => _instance;
  BatteryOptimization._internal();

  // تحسين استهلاك البطارية
  Future<void> optimizeForBattery() async {
    // تقليل تكرار المزامنة عند انخفاض البطارية
    // يمكن استخدام battery_plus package للمراقبة الفعلية
  }

  // التحقق من مستوى البطارية
  Future<bool> isBatteryLow() async {
    // محاكاة - يمكن استخدام battery_plus للتحقق الفعلي
    return false;
  }

  // تعديل إعدادات الخلفية حسب مستوى البطارية
  Future<void> adjustBackgroundSettings() async {
    final isLowBattery = await isBatteryLow();
    
    if (isLowBattery) {
      // تقليل تكرار المزامنة
      print('البطارية منخفضة - تقليل تكرار المزامنة');
    } else {
      // استخدام التكرار العادي
      print('البطارية جيدة - استخدام التكرار العادي');
    }
  }
}
