import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../models/Order.dart';
import '../utils/AppColors.dart';
import '../utils/AppConfig.dart';
import '../services/OrderService.dart';

class OrderDetailsPage extends StatefulWidget {
  final Order order;

  const OrderDetailsPage({
    super.key,
    required this.order,
  });

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  final OrderService _orderService = OrderService();
  late Order _order;

  @override
  void initState() {
    super.initState();
    _order = widget.order;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات الطلب الأساسية
            _buildOrderInfo(),
            
            SizedBox(height: 16.h),
            
            // معلومات العميل
            _buildCustomerInfo(),
            
            SizedBox(height: 16.h),
            
            // عناصر الطلب
            _buildOrderItems(),
            
            SizedBox(height: 16.h),
            
            // ملخص الفاتورة
            _buildOrderSummary(),
            
            SizedBox(height: 16.h),
            
            // معلومات الدفع
            _buildPaymentInfo(),
            
            if (_order.notes != null) ...[
              SizedBox(height: 16.h),
              _buildNotes(),
            ],
            
            SizedBox(height: 24.h),
            
            // أزرار الإجراءات
            _buildActionButtons(),
            
            SizedBox(height: 16.h),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: Text('تفاصيل الطلب #${_order.id.substring(0, 8)}'),
      actions: [
        // حالة الطلب
        Container(
          margin: EdgeInsets.only(left: 16.w),
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
          decoration: BoxDecoration(
            color: _getStatusColor(),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Text(
            _order.statusText,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.whiteColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildOrderInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الطلب',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 12.h),
          
          _buildInfoRow('رقم الطلب', '#${_order.id}'),
          _buildInfoRow('تاريخ الطلب', _formatDateTime(_order.createdAt)),
          _buildInfoRow('الحالة', _order.statusText),
          _buildInfoRow('الوقت المنقضي', _order.formattedTimeSinceCreated),
          
          if (_order.acceptedAt != null)
            _buildInfoRow('وقت القبول', _formatDateTime(_order.acceptedAt!)),
          
          if (_order.readyAt != null)
            _buildInfoRow('وقت الجاهزية', _formatDateTime(_order.readyAt!)),
          
          if (_order.deliveredAt != null)
            _buildInfoRow('وقت التسليم', _formatDateTime(_order.deliveredAt!)),
        ],
      ),
    );
  }

  Widget _buildCustomerInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات العميل',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 12.h),
          
          Row(
            children: [
              Icon(
                Icons.person_outline,
                color: AppColors.primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                _order.customerName,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.h),
          
          Row(
            children: [
              Icon(
                Icons.phone_outlined,
                color: AppColors.primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                _order.customerPhone,
                style: AppTextStyles.bodyMedium,
                textDirection: TextDirection.ltr,
              ),
              const Spacer(),
              IconButton(
                onPressed: () => _callCustomer(),
                icon: Icon(
                  Icons.call,
                  color: AppColors.successColor,
                  size: 20.sp,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.h),
          
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.location_on_outlined,
                color: AppColors.primaryColor,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  _order.customerAddress,
                  style: AppTextStyles.bodyMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItems() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'عناصر الطلب (${_order.items.length})',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 12.h),
          
          ..._order.items.map((item) => Container(
            margin: EdgeInsets.only(bottom: 12.h),
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: AppColors.backgroundColor,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                // صورة المنتج (إذا توفرت)
                if (item.productImage != null)
                  Container(
                    width: 50.w,
                    height: 50.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      image: DecorationImage(
                        image: NetworkImage(item.productImage!),
                        fit: BoxFit.cover,
                      ),
                    ),
                  )
                else
                  Container(
                    width: 50.w,
                    height: 50.w,
                    decoration: BoxDecoration(
                      color: AppColors.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      Icons.fastfood,
                      color: AppColors.primaryColor,
                      size: 24.sp,
                    ),
                  ),
                
                SizedBox(width: 12.w),
                
                // تفاصيل المنتج
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.productName,
                        style: AppTextStyles.bodyMedium.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      
                      if (item.notes != null) ...[
                        SizedBox(height: 4.h),
                        Text(
                          'ملاحظة: ${item.notes}',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                      
                      if (item.customizations != null && item.customizations!.isNotEmpty) ...[
                        SizedBox(height: 4.h),
                        Text(
                          'إضافات: ${item.customizations!.join(', ')}',
                          style: AppTextStyles.bodySmall.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // الكمية والسعر
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'الكمية: ${item.quantity}',
                      style: AppTextStyles.bodySmall,
                    ),
                    Text(
                      AppConfig.formatCurrency(item.price),
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      AppConfig.formatCurrency(item.total),
                      style: AppTextStyles.bodyMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildOrderSummary() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملخص الفاتورة',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 12.h),
          
          _buildSummaryRow('المجموع الفرعي', AppConfig.formatCurrency(_order.subtotal)),
          _buildSummaryRow('رسوم التوصيل', AppConfig.formatCurrency(_order.deliveryFee)),
          
          if (_order.discount > 0)
            _buildSummaryRow('الخصم', '-${AppConfig.formatCurrency(_order.discount)}', 
                color: AppColors.successColor),
          
          Divider(height: 20.h),
          
          _buildSummaryRow(
            'المجموع الكلي', 
            AppConfig.formatCurrency(_order.total),
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الدفع',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 12.h),
          
          _buildInfoRow('طريقة الدفع', _getPaymentMethodText(_order.paymentMethod)),
          _buildInfoRow('حالة الدفع', _getPaymentStatusText(_order.paymentStatus)),
        ],
      ),
    );
  }

  Widget _buildNotes() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'ملاحظات',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 12.h),
          
          Text(
            _order.notes!,
            style: AppTextStyles.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    final buttons = <Widget>[];

    if (_order.canAccept) {
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _acceptOrder(),
            style: AppButtonStyles.successButton,
            icon: Icon(Icons.check, size: 18.sp),
            label: const Text('قبول الطلب'),
          ),
        ),
      );
      
      buttons.add(SizedBox(width: 12.w));
      
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _rejectOrder(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorColor,
              foregroundColor: AppColors.whiteColor,
            ),
            icon: Icon(Icons.close, size: 18.sp),
            label: const Text('رفض الطلب'),
          ),
        ),
      );
    } else if (_order.canMarkReady) {
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _markOrderReady(),
            style: AppButtonStyles.successButton,
            icon: Icon(Icons.restaurant, size: 18.sp),
            label: const Text('الطلب جاهز'),
          ),
        ),
      );
    } else if (_order.canMarkDelivered) {
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _markOrderDelivered(),
            style: AppButtonStyles.successButton,
            icon: Icon(Icons.delivery_dining, size: 18.sp),
            label: const Text('تم التسليم'),
          ),
        ),
      );
    }

    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(children: buttons);
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {Color? color, bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal 
                ? AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.bold)
                : AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
          ),
          Text(
            value,
            style: isTotal 
                ? AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryColor,
                  )
                : AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _getPaymentMethodText(String method) {
    switch (method) {
      case 'cash':
        return 'نقداً عند التسليم';
      case 'card':
        return 'بطاقة ائتمان';
      case 'wallet':
        return 'محفظة إلكترونية';
      default:
        return method;
    }
  }

  String _getPaymentStatusText(String status) {
    switch (status) {
      case 'pending':
        return 'في انتظار الدفع';
      case 'paid':
        return 'تم الدفع';
      case 'failed':
        return 'فشل الدفع';
      default:
        return status;
    }
  }

  Color _getStatusColor() {
    switch (_order.status) {
      case 'new':
        return AppColors.newOrderColor;
      case 'accepted':
      case 'preparing':
        return AppColors.processingColor;
      case 'ready':
        return AppColors.readyColor;
      case 'delivered':
        return AppColors.deliveredColor;
      case 'cancelled':
        return AppColors.cancelledColor;
      default:
        return AppColors.textSecondary;
    }
  }

  void _callCustomer() {
    // يمكن إضافة منطق الاتصال هنا
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الاتصال بالعميل'),
        content: Text('هل تريد الاتصال بـ ${_order.customerName}؟\n${_order.customerPhone}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // إضافة منطق الاتصال الفعلي هنا
            },
            child: const Text('اتصال'),
          ),
        ],
      ),
    );
  }

  Future<void> _acceptOrder() async {
    final success = await _orderService.acceptOrder(_order.id);
    if (success) {
      setState(() {
        _order = _order.copyWith(status: 'accepted', acceptedAt: DateTime.now());
      });
      _showSuccessSnackBar('تم قبول الطلب بنجاح');
    } else {
      _showErrorSnackBar('فشل في قبول الطلب');
    }
  }

  Future<void> _rejectOrder() async {
    final reason = await _showRejectDialog();
    if (reason != null) {
      final success = await _orderService.rejectOrder(_order.id, reason);
      if (success) {
        setState(() {
          _order = _order.copyWith(status: 'cancelled', cancelReason: reason);
        });
        _showSuccessSnackBar('تم رفض الطلب');
      } else {
        _showErrorSnackBar('فشل في رفض الطلب');
      }
    }
  }

  Future<void> _markOrderReady() async {
    final success = await _orderService.markOrderReady(_order.id);
    if (success) {
      setState(() {
        _order = _order.copyWith(status: 'ready', readyAt: DateTime.now());
      });
      _showSuccessSnackBar('تم تحديد الطلب كجاهز');
    } else {
      _showErrorSnackBar('فشل في تحديث حالة الطلب');
    }
  }

  Future<void> _markOrderDelivered() async {
    final success = await _orderService.markOrderDelivered(_order.id);
    if (success) {
      setState(() {
        _order = _order.copyWith(status: 'delivered', deliveredAt: DateTime.now());
      });
      _showSuccessSnackBar('تم تحديد الطلب كمسلم');
    } else {
      _showErrorSnackBar('فشل في تحديث حالة الطلب');
    }
  }

  Future<String?> _showRejectDialog() async {
    final controller = TextEditingController();
    
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رفض الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى تحديد سبب رفض الطلب:'),
            SizedBox(height: 16.h),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                hintText: 'سبب الرفض',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final reason = controller.text.trim();
              Navigator.of(context).pop(reason.isNotEmpty ? reason : 'تم الرفض');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorColor,
            ),
            child: const Text('رفض الطلب'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
