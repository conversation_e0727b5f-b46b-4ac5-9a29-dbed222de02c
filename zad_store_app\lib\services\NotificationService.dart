import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/AppConfig.dart';
import '../models/Order.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();
  final AudioPlayer _audioPlayer = AudioPlayer();

  bool _isInitialized = false;
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  // تهيئة خدمة الإشعارات
  Future<void> initialize() async {
    if (_isInitialized) return;

    // طلب الأذونات
    await _requestPermissions();

    // إعدادات التهيئة لنظام Android
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // إعدادات التهيئة لنظام iOS
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    // دمج إعدادات التهيئة لجميع المنصات
    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    // تهيئة المكون الإضافي
    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // إنشاء قناة الإشعارات لنظام Android
    await _createNotificationChannel();

    // تحميل الإعدادات المحفوظة
    await _loadSettings();

    _isInitialized = true;
    print('تم تهيئة خدمة الإشعارات بنجاح');
  }

  // طلب الأذونات المطلوبة
  Future<void> _requestPermissions() async {
    // طلب إذن الإشعارات
    await Permission.notification.request();

    // طلب إذن الاهتزاز
    await Permission.systemAlertWindow.request();
  }

  // إنشاء قناة الإشعارات لنظام Android
  Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      AppConfig.notificationChannelId,
      AppConfig.notificationChannelName,
      description: AppConfig.notificationChannelDescription,
      importance: Importance.high,
      enableVibration: true,
      playSound: true,
    );

    await _flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  // معالج النقر على الإشعار
  void _onNotificationTapped(NotificationResponse response) {
    print('تم النقر على الإشعار: ${response.payload}');
    // يمكن إضافة منطق التنقل هنا
  }

  // تحميل إعدادات الإشعارات
  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;
    _soundEnabled = prefs.getBool('sound_enabled') ?? true;
    _vibrationEnabled = prefs.getBool('vibration_enabled') ?? true;
  }

  // حفظ إعدادات الإشعارات
  Future<void> _saveSettings() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', _notificationsEnabled);
    await prefs.setBool('sound_enabled', _soundEnabled);
    await prefs.setBool('vibration_enabled', _vibrationEnabled);
  }

  // إرسال إشعار طلب جديد
  Future<void> showNewOrderNotification(Order order) async {
    if (!_notificationsEnabled) return;

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: AndroidNotificationDetails(
        AppConfig.notificationChannelId,
        AppConfig.notificationChannelName,
        channelDescription: AppConfig.notificationChannelDescription,
        importance: Importance.high,
        priority: Priority.high,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
        color: Color(0xFFC3243B),
        largeIcon: DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
      ),
      iOS: DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    await _flutterLocalNotificationsPlugin.show(
      order.id.hashCode,
      '🔔 طلب جديد!',
      'طلب من ${order.customerName} بقيمة ${order.total.toStringAsFixed(2)} ر.ي',
      platformChannelSpecifics,
      payload: 'new_order:${order.id}',
    );

    // تشغيل صوت الإشعار
    if (_soundEnabled) {
      await _playNotificationSound(AppConfig.newOrderSound);
    }

    print('تم إرسال إشعار طلب جديد: ${order.id}');
  }

  // إرسال إشعار تحديث حالة الطلب
  Future<void> showOrderStatusUpdateNotification(Order order) async {
    if (!_notificationsEnabled) return;

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: AndroidNotificationDetails(
        AppConfig.notificationChannelId,
        AppConfig.notificationChannelName,
        channelDescription: AppConfig.notificationChannelDescription,
        importance: Importance.defaultImportance,
        priority: Priority.defaultPriority,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
      ),
      iOS: DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    await _flutterLocalNotificationsPlugin.show(
      order.id.hashCode + 1000,
      'تحديث حالة الطلب',
      'الطلب #${order.id.substring(0, 8)} - ${order.statusText}',
      platformChannelSpecifics,
      payload: 'order_update:${order.id}',
    );

    print('تم إرسال إشعار تحديث حالة الطلب: ${order.id}');
  }

  // إرسال إشعار عام
  Future<void> showGeneralNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_notificationsEnabled) return;

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: AndroidNotificationDetails(
        AppConfig.notificationChannelId,
        AppConfig.notificationChannelName,
        channelDescription: AppConfig.notificationChannelDescription,
        importance: Importance.defaultImportance,
        priority: Priority.defaultPriority,
        enableVibration: true,
        playSound: true,
        icon: '@mipmap/ic_launcher',
      ),
      iOS: DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      ),
    );

    await _flutterLocalNotificationsPlugin.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      platformChannelSpecifics,
      payload: payload,
    );

    print('تم إرسال إشعار عام: $title');
  }

  // تشغيل صوت الإشعار
  Future<void> _playNotificationSound(String soundPath) async {
    try {
      await _audioPlayer
          .play(AssetSource(soundPath.replaceFirst('assets/', '')));
    } catch (e) {
      print('خطأ في تشغيل صوت الإشعار: $e');
    }
  }

  // إرسال إشعار تذكير يومي
  Future<void> scheduleDailyReminder() async {
    if (!_notificationsEnabled) return;

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: AndroidNotificationDetails(
        AppConfig.notificationChannelId,
        AppConfig.notificationChannelName,
        channelDescription: AppConfig.notificationChannelDescription,
        importance: Importance.defaultImportance,
        priority: Priority.defaultPriority,
        enableVibration: false,
        playSound: false,
        icon: '@mipmap/ic_launcher',
      ),
      iOS: DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: false,
      ),
    );

    // جدولة إشعار يومي في الساعة 9 صباحاً
    await _flutterLocalNotificationsPlugin.periodicallyShow(
      999,
      '📊 تقرير يومي',
      'تحقق من طلبات اليوم ومبيعاتك',
      RepeatInterval.daily,
      platformChannelSpecifics,
      payload: 'daily_reminder',
    );

    print('تم جدولة التذكير اليومي');
  }

  // إلغاء إشعار محدد
  Future<void> cancelNotification(int id) async {
    await _flutterLocalNotificationsPlugin.cancel(id);
  }

  // إلغاء جميع الإشعارات
  Future<void> cancelAllNotifications() async {
    await _flutterLocalNotificationsPlugin.cancelAll();
  }

  // تفعيل/تعطيل الإشعارات
  Future<void> setNotificationsEnabled(bool enabled) async {
    _notificationsEnabled = enabled;
    await _saveSettings();

    if (!enabled) {
      await cancelAllNotifications();
    }
  }

  // تفعيل/تعطيل الصوت
  Future<void> setSoundEnabled(bool enabled) async {
    _soundEnabled = enabled;
    await _saveSettings();
  }

  // تفعيل/تعطيل الاهتزاز
  Future<void> setVibrationEnabled(bool enabled) async {
    _vibrationEnabled = enabled;
    await _saveSettings();
  }

  // الحصول على حالة الإشعارات
  bool get notificationsEnabled => _notificationsEnabled;
  bool get soundEnabled => _soundEnabled;
  bool get vibrationEnabled => _vibrationEnabled;

  // التحقق من أذونات الإشعارات
  Future<bool> hasNotificationPermission() async {
    final status = await Permission.notification.status;
    return status.isGranted;
  }

  // طلب أذونات الإشعارات
  Future<bool> requestNotificationPermission() async {
    final status = await Permission.notification.request();
    return status.isGranted;
  }

  // تشغيل صوت الإشعار
  Future<void> playNotificationSound() async {
    if (!_soundEnabled) return;

    try {
      // تشغيل صوت الطلب الجديد
      await _audioPlayer.play(AssetSource('sounds/new_order.mp3'));
    } catch (e) {
      print('خطأ في تشغيل صوت الإشعار: $e');
    }
  }


}
