# إعداد Firebase للتطبيق

## الخطوات المطلوبة لإعداد Firebase

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. أنشئ مشروع جديد أو استخدم مشروع موجود
3. أضف تطبيق Android للمشروع

### 2. تحميل ملف التكوين
1. حمل ملف `google-services.json` من Firebase Console
2. ضع الملف في مجلد `android/app/`

### 3. إعداد Firestore Database
1. في Firebase Console، اذهب إلى Firestore Database
2. أنشئ قاعدة بيانات جديدة
3. أنشئ المجموعات التالية:

#### هيكل قاعدة البيانات:
```
store_notifications/
  ├── ملحمة المعلم/
  │   └── notifications/
  │       ├── {notification_id}/
  │       │   ├── type: "new_order"
  │       │   ├── title: "طلب جديد"
  │       │   ├── body: "لديك طلب جديد من أحمد محمد"
  │       │   ├── order_data: {...}
  │       │   ├── created_at: timestamp
  │       │   ├── read: false
  │       │   └── read_at: null
  │       └── ...
  └── ...
```

### 4. قواعد الأمان في Firestore
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // قواعد للإشعارات
    match /store_notifications/{storeId}/notifications/{notificationId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

### 5. إعداد Cloud Messaging
1. في Firebase Console، اذهب إلى Cloud Messaging
2. أنشئ topics للمحلات:
   - `store_ملحمة_المعلم`
   - `all_stores`

### 6. إرسال إشعار تجريبي
يمكنك إرسال إشعار تجريبي من Firebase Console أو باستخدام API:

#### مثال على إرسال إشعار عبر API:
```bash
curl -X POST https://fcm.googleapis.com/fcm/send \
  -H "Authorization: key=YOUR_SERVER_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "to": "/topics/store_ملحمة_المعلم",
    "notification": {
      "title": "طلب جديد",
      "body": "لديك طلب جديد من أحمد محمد"
    },
    "data": {
      "type": "new_order",
      "order_id": "12345"
    }
  }'
```

### 7. إضافة بيانات تجريبية
أضف إشعار تجريبي في Firestore:

```javascript
// في مجموعة store_notifications/ملحمة المعلم/notifications
{
  "type": "new_order",
  "title": "طلب جديد",
  "body": "لديك طلب جديد من أحمد محمد",
  "order_data": {
    "id": "12345",
    "customer_name": "أحمد محمد",
    "customer_phone": "777123456",
    "total": 25000,
    "items": [
      {
        "product_name": "لحم غنم",
        "quantity": 2,
        "price": 12500
      }
    ]
  },
  "created_at": "2024-01-15T10:30:00Z",
  "read": false,
  "read_at": null
}
```

## استخدام الخدمة في التطبيق

### 1. تهيئة الخدمة
```dart
final firebaseService = FirebaseNotificationService();
await firebaseService.initialize();
```

### 2. بدء الاستماع للإشعارات
```dart
await firebaseService.startListeningToStoreNotifications('ملحمة المعلم');
```

### 3. إيقاف الاستماع
```dart
await firebaseService.stopListening();
```

## الميزات المتاحة

### 1. الاستماع للإشعارات في الوقت الفعلي
- يستمع التطبيق للإشعارات الجديدة من Firestore
- يعرض إشعارات محلية عند وصول إشعارات جديدة
- يشغل أصوات تنبيه للطلبات الجديدة

### 2. أنواع الإشعارات المدعومة
- `new_order`: طلب جديد
- `order_update`: تحديث حالة الطلب
- `store_message`: رسالة للمحل
- `general`: إشعار عام

### 3. إدارة حالة الإشعارات
- تحديد الإشعارات كمقروءة تلقائياً
- حفظ وقت القراءة
- فلترة الإشعارات غير المقروءة

### 4. الاشتراك في Topics
- اشتراك تلقائي في topic المحل
- إلغاء الاشتراك عند إغلاق التطبيق

## استكشاف الأخطاء

### 1. التحقق من الاتصال
```dart
print('FCM Token: ${firebaseService.fcmToken}');
```

### 2. فحص الإشعارات في Firestore
تأكد من وجود الإشعارات في المسار الصحيح:
`store_notifications/ملحمة المعلم/notifications`

### 3. التحقق من الأذونات
تأكد من منح التطبيق أذونات الإشعارات في إعدادات الجهاز.

### 4. فحص Logs
راقب logs التطبيق للتأكد من:
- تهيئة Firebase بنجاح
- بدء الاستماع للإشعارات
- استلام الإشعارات الجديدة

## ملاحظات مهمة

1. **معرف المحل**: تأكد من تحديث `FirebaseConfig.defaultStoreId` ليطابق اسم المحل في Firestore
2. **الأذونات**: التطبيق يطلب أذونات الإشعارات تلقائياً
3. **الشبكة**: يتطلب اتصال إنترنت للعمل مع Firebase
4. **التخزين المحلي**: يحفظ التطبيق الإشعارات محلياً للعمل بدون إنترنت

## الدعم الفني

في حالة وجود مشاكل:
1. تحقق من إعدادات Firebase
2. راجع logs التطبيق
3. تأكد من صحة ملف `google-services.json`
4. تحقق من قواعد الأمان في Firestore
