import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/AppColors.dart';
import '../utils/AppConfig.dart';
import '../services/AuthService.dart';
import '../services/BackgroundService.dart';
import 'HomePage.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  final AuthService _authService = AuthService();
  final BackgroundService _backgroundService = BackgroundService();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  bool _rememberMe = false;

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: Column(
              children: [
                SizedBox(height: 60.h),
                
                // الشعار والعنوان
                _buildHeader(),
                
                SizedBox(height: 50.h),
                
                // نموذج تسجيل الدخول
                _buildLoginForm(),
                
                SizedBox(height: 30.h),
                
                // روابط إضافية
                _buildFooterLinks(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        // الشعار
        Container(
          width: 100.w,
          height: 100.w,
          decoration: BoxDecoration(
            color: AppColors.whiteColor,
            borderRadius: BorderRadius.circular(25.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Icon(
            Icons.store,
            size: 50.sp,
            color: AppColors.primaryColor,
          ),
        ),
        
        SizedBox(height: 20.h),
        
        // العنوان
        Text(
          'مرحباً بك',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.whiteColor,
            fontSize: 28.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        
        SizedBox(height: 8.h),
        
        Text(
          'سجل دخولك لإدارة محلك',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.whiteColor.withOpacity(0.9),
            fontSize: 16.sp,
          ),
        ),
      ],
    );
  }

  Widget _buildLoginForm() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: AppColors.whiteColor,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // عنوان النموذج
            Text(
              'تسجيل الدخول',
              style: AppTextStyles.titleMedium.copyWith(
                fontSize: 22.sp,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 30.h),
            
            // حقل البريد الإلكتروني
            _buildEmailField(),
            
            SizedBox(height: 20.h),
            
            // حقل كلمة المرور
            _buildPasswordField(),
            
            SizedBox(height: 15.h),
            
            // تذكرني ونسيت كلمة المرور
            _buildRememberAndForgot(),
            
            SizedBox(height: 30.h),
            
            // زر تسجيل الدخول
            _buildLoginButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: 'البريد الإلكتروني',
        hintText: '<EMAIL>',
        prefixIcon: Icon(Icons.email_outlined, color: AppColors.primaryColor),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
        ),
        filled: true,
        fillColor: AppColors.backgroundColor,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال البريد الإلكتروني';
        }
        if (!AppConfig.isValidEmail(value)) {
          return 'يرجى إدخال بريد إلكتروني صحيح';
        }
        return null;
      },
    );
  }

  Widget _buildPasswordField() {
    return TextFormField(
      controller: _passwordController,
      obscureText: _obscurePassword,
      textDirection: TextDirection.ltr,
      decoration: InputDecoration(
        labelText: 'كلمة المرور',
        hintText: '••••••••',
        prefixIcon: Icon(Icons.lock_outlined, color: AppColors.primaryColor),
        suffixIcon: IconButton(
          icon: Icon(
            _obscurePassword ? Icons.visibility_outlined : Icons.visibility_off_outlined,
            color: AppColors.textSecondary,
          ),
          onPressed: () {
            setState(() {
              _obscurePassword = !_obscurePassword;
            });
          },
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.borderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12.r),
          borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
        ),
        filled: true,
        fillColor: AppColors.backgroundColor,
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'يرجى إدخال كلمة المرور';
        }
        if (value.length < AppConfig.minPasswordLength) {
          return 'كلمة المرور يجب أن تكون ${AppConfig.minPasswordLength} أحرف على الأقل';
        }
        return null;
      },
    );
  }

  Widget _buildRememberAndForgot() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // تذكرني
        Row(
          children: [
            Checkbox(
              value: _rememberMe,
              onChanged: (value) {
                setState(() {
                  _rememberMe = value ?? false;
                });
              },
              activeColor: AppColors.primaryColor,
            ),
            Text(
              'تذكرني',
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
        
        // نسيت كلمة المرور
        TextButton(
          onPressed: _showForgotPasswordDialog,
          child: Text(
            'نسيت كلمة المرور؟',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return ElevatedButton(
      onPressed: _isLoading ? null : _handleLogin,
      style: AppButtonStyles.primaryButton.copyWith(
        minimumSize: MaterialStateProperty.all(Size(double.infinity, 50.h)),
      ),
      child: _isLoading
          ? SizedBox(
              width: 20.w,
              height: 20.w,
              child: const CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppColors.whiteColor),
              ),
            )
          : Text(
              'تسجيل الدخول',
              style: AppTextStyles.buttonText.copyWith(fontSize: 16.sp),
            ),
    );
  }

  Widget _buildFooterLinks() {
    return Column(
      children: [
        Text(
          'لا تملك حساب؟',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.whiteColor.withOpacity(0.8),
          ),
        ),
        
        SizedBox(height: 8.h),
        
        TextButton(
          onPressed: _showRegistrationInfo,
          child: Text(
            'تواصل معنا للتسجيل',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.whiteColor,
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin() async {
    Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomePage()),
          );
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await _authService.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (result.isSuccess) {
        // بدء خدمة المراقبة في الخلفية
        await _backgroundService.startOrderMonitoring();
        
        // الانتقال إلى الصفحة الرئيسية
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const HomePage()),
          );
        }
      } else {
        _showErrorSnackBar(result.message);
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ غير متوقع');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.errorColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.r),
          ),
        ),
      );
    }
  }

  void _showForgotPasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('نسيت كلمة المرور؟'),
        content: const Text(
          'للحصول على كلمة مرور جديدة، يرجى التواصل مع فريق الدعم الفني.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // يمكن إضافة منطق التواصل مع الدعم هنا
            },
            child: const Text('تواصل مع الدعم'),
          ),
        ],
      ),
    );
  }

  void _showRegistrationInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل محل جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('للتسجيل كمحل جديد في تطبيق زاد، يرجى التواصل معنا:'),
            SizedBox(height: 16.h),
            Text('📞 ${AppConfig.supportPhone}'),
            SizedBox(height: 8.h),
            Text('📧 ${AppConfig.supportEmail}'),
            SizedBox(height: 8.h),
            Text('💬 ${AppConfig.supportWhatsApp}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
