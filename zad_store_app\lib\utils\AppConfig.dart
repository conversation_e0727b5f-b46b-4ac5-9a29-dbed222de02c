class AppConfig {
  // معلومات التطبيق
  static const String appName = "زاد للمحلات";
  static const String appVersion = "1.0.0";
  static const String appDescription = "إدارة الطلبات والمبيعات لأصحاب المحلات";

  // إعدادات التطبيق
  static const int splashDuration = 3; // بالثواني
  static const double animationDuration = 0.3; // بالثواني

  // إعدادات الشبكة
  static const String baseUrl = "https://api.zad-stores.com";
  static const String ordersEndpoint = "/api/orders";
  static const String storeEndpoint = "/api/store";
  static const String authEndpoint = "/api/auth";
  static const int connectionTimeout = 30; // بالثواني
  static const int receiveTimeout = 30; // بالثواني

  // إعدادات التخزين المحلي
  static const String storeTokenKey = "store_token";
  static const String storeDataKey = "store_data";
  static const String settingsKey = "app_settings";
  static const String ordersKey = "cached_orders";
  static const String dailyReportKey = "daily_report";

  // إعدادات الإشعارات
  static const bool defaultNotificationsEnabled = true;
  static const bool defaultSoundEnabled = true;
  static const bool defaultVibrationEnabled = true;
  static const String notificationChannelId = "zad_store_orders";
  static const String notificationChannelName = "طلبات زاد";
  static const String notificationChannelDescription = "إشعارات الطلبات الجديدة";

  // إعدادات العمل في الخلفية
  static const String backgroundTaskName = "orderSyncTask";
  static const int backgroundSyncInterval = 15; // بالدقائق
  static const int maxRetryAttempts = 3;

  // حالات الطلبات
  static const String orderStatusNew = "new";
  static const String orderStatusAccepted = "accepted";
  static const String orderStatusPreparing = "preparing";
  static const String orderStatusReady = "ready";
  static const String orderStatusDelivered = "delivered";
  static const String orderStatusCancelled = "cancelled";

  // أصوات الإشعارات
  static const String newOrderSound = "assets/sounds/new_order.mp3";
  static const String successSound = "assets/sounds/success.mp3";
  static const String errorSound = "assets/sounds/error.mp3";

  // إعدادات التقارير
  static const int reportRetentionDays = 30; // عدد أيام الاحتفاظ بالتقارير
  static const List<String> reportTypes = [
    "daily",
    "weekly", 
    "monthly"
  ];

  // أرقام الاتصال والدعم
  static const String supportPhone = "+967 777 123 456";
  static const String supportEmail = "<EMAIL>";
  static const String supportWhatsApp = "+967 777 123 456";

  // روابط التواصل الاجتماعي
  static const String facebookUrl = "https://facebook.com/zad";
  static const String instagramUrl = "https://instagram.com/zad";
  static const String twitterUrl = "https://twitter.com/zad";

  // إعدادات الأمان
  static const int sessionTimeout = 24; // بالساعات
  static const int maxLoginAttempts = 5;
  static const int lockoutDuration = 30; // بالدقائق

  // إعدادات الواجهة
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 8.0;
  static const double defaultBorderRadius = 12.0;
  static const double defaultElevation = 2.0;

  // إعدادات الطلبات
  static const int maxOrdersPerPage = 20;
  static const int autoRefreshInterval = 30; // بالثواني
  static const int orderTimeoutMinutes = 15; // مهلة قبول الطلب

  // رسائل النظام
  static const Map<String, String> systemMessages = {
    'login_success': 'تم تسجيل الدخول بنجاح',
    'login_failed': 'فشل في تسجيل الدخول',
    'order_accepted': 'تم قبول الطلب',
    'order_rejected': 'تم رفض الطلب',
    'connection_error': 'خطأ في الاتصال بالخادم',
    'no_internet': 'لا يوجد اتصال بالإنترنت',
    'sync_success': 'تم تحديث البيانات',
    'sync_failed': 'فشل في تحديث البيانات',
  };

  // ألوان حالات الطلبات
  static const Map<String, String> orderStatusColors = {
    'new': '#3498DB',
    'accepted': '#F39C12',
    'preparing': '#E67E22',
    'ready': '#27AE60',
    'delivered': '#95A5A6',
    'cancelled': '#E74C3C',
  };

  // نصوص حالات الطلبات
  static const Map<String, String> orderStatusTexts = {
    'new': 'طلب جديد',
    'accepted': 'تم القبول',
    'preparing': 'قيد التحضير',
    'ready': 'جاهز للتسليم',
    'delivered': 'تم التسليم',
    'cancelled': 'ملغي',
  };

  // إعدادات التحقق من صحة البيانات
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 20;
  static const int minStoreNameLength = 3;
  static const int maxStoreNameLength = 50;

  // إعدادات الملفات
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png'];
  static const int maxImageSize = 5 * 1024 * 1024; // 5 MB

  // إعدادات الموقع
  static const double defaultLatitude = 15.3694; // صنعاء
  static const double defaultLongitude = 44.1910; // صنعاء
  static const double locationAccuracy = 100.0; // بالمتر

  // إعدادات العملة
  static const String currency = "ريال يمني";
  static const String currencySymbol = "ر.ي";
  static const String currencyCode = "YER";

  // إعدادات التوقيت
  static const String timeZone = "Asia/Aden";
  static const String dateFormat = "yyyy-MM-dd";
  static const String timeFormat = "HH:mm";
  static const String dateTimeFormat = "yyyy-MM-dd HH:mm";

  // إعدادات الأداء
  static const int maxCacheSize = 50 * 1024 * 1024; // 50 MB
  static const int cacheExpirationHours = 24;
  static const int maxConcurrentRequests = 5;

  // إعدادات التطوير
  static const bool debugMode = true;
  static const bool enableLogging = true;
  static const String logLevel = "debug"; // debug, info, warning, error

  // الحصول على URL كامل
  static String getFullUrl(String endpoint) {
    return baseUrl + endpoint;
  }

  // التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // التحقق من صحة رقم الهاتف اليمني
  static bool isValidYemeniPhone(String phone) {
    return RegExp(r'^(\+967|967|0)?[7][0-9]{8}$').hasMatch(phone);
  }

  // تنسيق العملة
  static String formatCurrency(double amount) {
    return "${amount.toStringAsFixed(2)} $currencySymbol";
  }
}
