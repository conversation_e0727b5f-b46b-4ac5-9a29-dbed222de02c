import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/AppColors.dart';
import '../services/OrderService.dart';
import '../models/Order.dart';
import '../widgets/OrderCard.dart';
import 'OrderDetailsPage.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> with TickerProviderStateMixin {
  final OrderService _orderService = OrderService();
  late TabController _tabController;
  
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: _buildAppBar(),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),
          
          // قائمة الطلبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOrdersList(_getFilteredOrders(_orderService.orders)),
                _buildOrdersList(_getFilteredOrders(_orderService.getOrdersByStatus('new'))),
                _buildOrdersList(_getFilteredOrders(_orderService.getOrdersByStatus('accepted'))),
                _buildOrdersList(_getFilteredOrders(_orderService.getOrdersByStatus('preparing'))),
                _buildOrdersList(_getFilteredOrders(_orderService.getOrdersByStatus('ready'))),
                _buildOrdersList(_getFilteredOrders(_orderService.getOrdersByStatus('delivered'))),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('إدارة الطلبات'),
      bottom: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppColors.whiteColor,
        labelColor: AppColors.whiteColor,
        unselectedLabelColor: AppColors.whiteColor.withOpacity(0.7),
        tabs: [
          Tab(text: 'الكل (${_orderService.orders.length})'),
          Tab(text: 'جديد (${_orderService.getOrdersByStatus('new').length})'),
          Tab(text: 'مقبول (${_orderService.getOrdersByStatus('accepted').length})'),
          Tab(text: 'قيد التحضير (${_orderService.getOrdersByStatus('preparing').length})'),
          Tab(text: 'جاهز (${_orderService.getOrdersByStatus('ready').length})'),
          Tab(text: 'مسلم (${_orderService.getOrdersByStatus('delivered').length})'),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: EdgeInsets.all(16.w),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'البحث في الطلبات...',
          prefixIcon: Icon(Icons.search, color: AppColors.textSecondary),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: Icon(Icons.clear, color: AppColors.textSecondary),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(color: AppColors.borderColor),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12.r),
            borderSide: BorderSide(color: AppColors.primaryColor, width: 2),
          ),
          filled: true,
          fillColor: AppColors.whiteColor,
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildOrdersList(List<Order> orders) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64.sp,
              color: AppColors.textLight,
            ),
            SizedBox(height: 16.h),
            Text(
              _searchQuery.isNotEmpty ? 'لا توجد نتائج للبحث' : 'لا توجد طلبات',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textLight,
              ),
            ),
            if (_searchQuery.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Text(
                'جرب البحث بكلمات مختلفة',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textLight,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshOrders,
      color: AppColors.primaryColor,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return OrderCard(
            order: order,
            onTap: () => _openOrderDetails(order),
            onAccept: order.canAccept ? () => _acceptOrder(order) : null,
            onReject: order.canCancel ? () => _rejectOrder(order) : null,
            onMarkReady: order.canMarkReady ? () => _markOrderReady(order) : null,
            onMarkDelivered: order.canMarkDelivered ? () => _markOrderDelivered(order) : null,
          );
        },
      ),
    );
  }

  List<Order> _getFilteredOrders(List<Order> orders) {
    if (_searchQuery.isEmpty) return orders;
    
    return _orderService.searchOrders(_searchQuery);
  }

  Future<void> _refreshOrders() async {
    await _orderService.refreshOrders();
    if (mounted) {
      setState(() {});
    }
  }

  void _openOrderDetails(Order order) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OrderDetailsPage(order: order),
      ),
    );
  }

  Future<void> _acceptOrder(Order order) async {
    final success = await _orderService.acceptOrder(order.id);
    if (success) {
      _showSuccessSnackBar('تم قبول الطلب بنجاح');
      setState(() {});
    } else {
      _showErrorSnackBar('فشل في قبول الطلب');
    }
  }

  Future<void> _rejectOrder(Order order) async {
    final reason = await _showRejectDialog();
    if (reason != null) {
      final success = await _orderService.rejectOrder(order.id, reason);
      if (success) {
        _showSuccessSnackBar('تم رفض الطلب');
        setState(() {});
      } else {
        _showErrorSnackBar('فشل في رفض الطلب');
      }
    }
  }

  Future<void> _markOrderReady(Order order) async {
    final success = await _orderService.markOrderReady(order.id);
    if (success) {
      _showSuccessSnackBar('تم تحديد الطلب كجاهز');
      setState(() {});
    } else {
      _showErrorSnackBar('فشل في تحديث حالة الطلب');
    }
  }

  Future<void> _markOrderDelivered(Order order) async {
    final success = await _orderService.markOrderDelivered(order.id);
    if (success) {
      _showSuccessSnackBar('تم تحديد الطلب كمسلم');
      setState(() {});
    } else {
      _showErrorSnackBar('فشل في تحديث حالة الطلب');
    }
  }

  Future<String?> _showRejectDialog() async {
    final controller = TextEditingController();
    
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رفض الطلب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('يرجى تحديد سبب رفض الطلب:'),
            SizedBox(height: 16.h),
            TextField(
              controller: controller,
              decoration: const InputDecoration(
                hintText: 'سبب الرفض',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final reason = controller.text.trim();
              Navigator.of(context).pop(reason.isNotEmpty ? reason : 'تم الرفض');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorColor,
            ),
            child: const Text('رفض الطلب'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.successColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.errorColor,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10.r),
        ),
      ),
    );
  }
}
