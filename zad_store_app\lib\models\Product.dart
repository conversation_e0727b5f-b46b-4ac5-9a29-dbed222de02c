class Product {
  final String id;
  final String storeId;
  final String name;
  final String description;
  final double price;
  final String? imageUrl;
  final String category;
  final bool isAvailable;
  final bool isFeatured;
  final int stockQuantity;
  final double? discountPrice;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic>? metadata;
  final List<String>? tags;
  final double rating;
  final int reviewCount;

  Product({
    required this.id,
    required this.storeId,
    required this.name,
    required this.description,
    required this.price,
    this.imageUrl,
    required this.category,
    this.isAvailable = true,
    this.isFeatured = false,
    this.stockQuantity = 0,
    this.discountPrice,
    required this.createdAt,
    this.updatedAt,
    this.metadata,
    this.tags,
    this.rating = 0.0,
    this.reviewCount = 0,
  });

  // تحويل من JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] ?? '',
      storeId: json['store_id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      price: (json['price'] ?? 0.0).toDouble(),
      imageUrl: json['image_url'],
      category: json['category'] ?? '',
      isAvailable: json['is_available'] ?? true,
      isFeatured: json['is_featured'] ?? false,
      stockQuantity: json['stock_quantity'] ?? 0,
      discountPrice: json['discount_price'] != null 
          ? (json['discount_price']).toDouble() 
          : null,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      metadata: json['metadata'],
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      rating: (json['rating'] ?? 0.0).toDouble(),
      reviewCount: json['review_count'] ?? 0,
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'store_id': storeId,
      'name': name,
      'description': description,
      'price': price,
      'image_url': imageUrl,
      'category': category,
      'is_available': isAvailable,
      'is_featured': isFeatured,
      'stock_quantity': stockQuantity,
      'discount_price': discountPrice,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'metadata': metadata,
      'tags': tags,
      'rating': rating,
      'review_count': reviewCount,
    };
  }

  // نسخ مع تعديل
  Product copyWith({
    String? id,
    String? storeId,
    String? name,
    String? description,
    double? price,
    String? imageUrl,
    String? category,
    bool? isAvailable,
    bool? isFeatured,
    int? stockQuantity,
    double? discountPrice,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
    List<String>? tags,
    double? rating,
    int? reviewCount,
  }) {
    return Product(
      id: id ?? this.id,
      storeId: storeId ?? this.storeId,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      isAvailable: isAvailable ?? this.isAvailable,
      isFeatured: isFeatured ?? this.isFeatured,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      discountPrice: discountPrice ?? this.discountPrice,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
      tags: tags ?? this.tags,
      rating: rating ?? this.rating,
      reviewCount: reviewCount ?? this.reviewCount,
    );
  }

  // الحصول على السعر النهائي (مع الخصم إن وجد)
  double get finalPrice => discountPrice ?? price;

  // التحقق من وجود خصم
  bool get hasDiscount => discountPrice != null && discountPrice! < price;

  // حساب نسبة الخصم
  double get discountPercentage {
    if (!hasDiscount) return 0.0;
    return ((price - discountPrice!) / price) * 100;
  }

  // التحقق من توفر المنتج
  bool get isInStock => stockQuantity > 0;

  // التحقق من أن المنتج متاح للطلب
  bool get canOrder => isAvailable && isInStock;

  // الحصول على نص حالة التوفر
  String get availabilityText {
    if (!isAvailable) return 'غير متاح';
    if (!isInStock) return 'نفد المخزون';
    if (stockQuantity <= 5) return 'كمية محدودة';
    return 'متوفر';
  }

  // الحصول على لون حالة التوفر
  String get availabilityColor {
    if (!isAvailable || !isInStock) return '#E74C3C';
    if (stockQuantity <= 5) return '#F39C12';
    return '#27AE60';
  }

  // تنسيق السعر
  String get formattedPrice {
    return "${price.toStringAsFixed(2)} ر.ي";
  }

  // تنسيق السعر مع الخصم
  String get formattedFinalPrice {
    return "${finalPrice.toStringAsFixed(2)} ر.ي";
  }

  // تنسيق نسبة الخصم
  String get formattedDiscountPercentage {
    return "${discountPercentage.toStringAsFixed(0)}%";
  }

  // تنسيق التقييم
  String get formattedRating {
    return rating.toStringAsFixed(1);
  }

  // الحصول على نجوم التقييم
  List<bool> get ratingStars {
    List<bool> stars = [];
    for (int i = 1; i <= 5; i++) {
      stars.add(i <= rating.round());
    }
    return stars;
  }

  @override
  String toString() {
    return 'Product{id: $id, name: $name, price: $price, isAvailable: $isAvailable}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Product &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

// فئات المنتجات
class ProductCategory {
  static const String food = 'food';
  static const String beverages = 'beverages';
  static const String groceries = 'groceries';
  static const String pharmacy = 'pharmacy';
  static const String electronics = 'electronics';
  static const String clothing = 'clothing';
  static const String books = 'books';
  static const String beauty = 'beauty';
  static const String sports = 'sports';
  static const String home = 'home';
  static const String other = 'other';

  static const Map<String, String> categoryNames = {
    food: 'طعام',
    beverages: 'مشروبات',
    groceries: 'بقالة',
    pharmacy: 'صيدلية',
    electronics: 'إلكترونيات',
    clothing: 'ملابس',
    books: 'كتب',
    beauty: 'تجميل',
    sports: 'رياضة',
    home: 'منزل',
    other: 'أخرى',
  };

  static String getCategoryName(String category) {
    return categoryNames[category] ?? 'غير محدد';
  }

  static List<String> getAllCategories() {
    return categoryNames.keys.toList();
  }
}
