import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/AppColors.dart';
import '../utils/AppConfig.dart';
import '../services/AuthService.dart';
import '../models/Store.dart';
import 'LoginPage.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final AuthService _authService = AuthService();
  Store? _store;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _store = _authService.currentStore;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: _buildAppBar(),
      body: _store == null ? _buildErrorWidget() : _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('الملف الشخصي'),
      actions: [
        IconButton(
          onPressed: _showEditDialog,
          icon: Icon(Icons.edit, size: 24.sp),
        ),
      ],
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.sp,
            color: AppColors.errorColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'خطأ في تحميل بيانات المحل',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.errorColor,
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('العودة'),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // معلومات المحل الأساسية
          _buildStoreHeader(),
          
          SizedBox(height: 16.h),
          
          // معلومات الاتصال
          _buildContactInfo(),
          
          SizedBox(height: 16.h),
          
          // معلومات الموقع
          _buildLocationInfo(),
          
          SizedBox(height: 16.h),
          
          // أوقات العمل
          _buildWorkingHours(),
          
          SizedBox(height: 16.h),
          
          // الإحصائيات
          _buildStatistics(),
          
          SizedBox(height: 16.h),
          
          // الإعدادات والخيارات
          _buildSettingsSection(),
          
          SizedBox(height: 24.h),
          
          // زر تسجيل الخروج
          _buildLogoutButton(),
        ],
      ),
    );
  }

  Widget _buildStoreHeader() {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: AppCardStyles.gradientCard,
      child: Column(
        children: [
          // صورة المحل أو أيقونة افتراضية
          Container(
            width: 80.w,
            height: 80.w,
            decoration: BoxDecoration(
              color: AppColors.whiteColor,
              borderRadius: BorderRadius.circular(40.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: _store!.logoUrl != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(40.r),
                    child: Image.network(
                      _store!.logoUrl!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Icon(
                        Icons.store,
                        size: 40.sp,
                        color: AppColors.primaryColor,
                      ),
                    ),
                  )
                : Icon(
                    Icons.store,
                    size: 40.sp,
                    color: AppColors.primaryColor,
                  ),
          ),
          
          SizedBox(height: 16.h),
          
          // اسم المحل
          Text(
            _store!.name,
            style: AppTextStyles.titleLarge.copyWith(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: 8.h),
          
          // وصف المحل
          Text(
            _store!.description,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          
          SizedBox(height: 12.h),
          
          // حالة المحل
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: _store!.isActive ? AppColors.successColor : AppColors.errorColor,
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _store!.isActive ? Icons.check_circle : Icons.cancel,
                  color: AppColors.whiteColor,
                  size: 16.sp,
                ),
                SizedBox(width: 4.w),
                Text(
                  _store!.statusText,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.whiteColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الاتصال',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          _buildInfoRow(
            Icons.person_outline,
            'صاحب المحل',
            _store!.ownerName,
          ),
          
          _buildInfoRow(
            Icons.email_outlined,
            'البريد الإلكتروني',
            _store!.email,
          ),
          
          _buildInfoRow(
            Icons.phone_outlined,
            'رقم الهاتف',
            _store!.phone,
          ),
        ],
      ),
    );
  }

  Widget _buildLocationInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات الموقع',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          _buildInfoRow(
            Icons.location_on_outlined,
            'العنوان',
            _store!.address,
          ),
          
          _buildInfoRow(
            Icons.category_outlined,
            'التصنيف',
            _store!.category,
          ),
        ],
      ),
    );
  }

  Widget _buildWorkingHours() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أوقات العمل',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          if (_store!.workingHours.isEmpty)
            Text(
              'لم يتم تحديد أوقات العمل',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            )
          else
            ..._store!.workingHours.map((schedule) => Padding(
              padding: EdgeInsets.only(bottom: 8.h),
              child: Row(
                children: [
                  Icon(
                    Icons.access_time,
                    color: AppColors.primaryColor,
                    size: 16.sp,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    schedule,
                    style: AppTextStyles.bodyMedium,
                  ),
                ],
              ),
            )),
        ],
      ),
    );
  }

  Widget _buildStatistics() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإحصائيات',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'التقييم',
                  _store!.rating.toStringAsFixed(1),
                  Icons.star_outline,
                  AppColors.warningColor,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatItem(
                  'إجمالي الطلبات',
                  '${_store!.totalOrders}',
                  Icons.shopping_bag_outlined,
                  AppColors.infoColor,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 12.h),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'إجمالي الإيرادات',
                  AppConfig.formatCurrency(_store!.totalRevenue),
                  Icons.attach_money,
                  AppColors.successColor,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatItem(
                  'تاريخ التسجيل',
                  '${_store!.createdAt.day}/${_store!.createdAt.month}/${_store!.createdAt.year}',
                  Icons.calendar_today,
                  AppColors.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20.sp,
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإعدادات',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          _buildSettingItem(
            Icons.edit_outlined,
            'تعديل معلومات المحل',
            () => _showEditDialog(),
          ),
          
          _buildSettingItem(
            Icons.lock_outline,
            'تغيير كلمة المرور',
            () => _showChangePasswordDialog(),
          ),
          
          _buildSettingItem(
            Icons.notifications_outlined,
            'إعدادات الإشعارات',
            () => _showNotificationSettings(),
          ),
          
          _buildSettingItem(
            Icons.help_outline,
            'المساعدة والدعم',
            () => _showSupportDialog(),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingItem(IconData icon, String title, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8.r),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.primaryColor,
              size: 20.sp,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.bodyMedium,
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textSecondary,
              size: 16.sp,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _showLogoutDialog,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.errorColor,
          foregroundColor: AppColors.whiteColor,
          padding: EdgeInsets.symmetric(vertical: 16.h),
        ),
        icon: Icon(Icons.logout, size: 20.sp),
        label: Text(
          'تسجيل الخروج',
          style: AppTextStyles.buttonText,
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: AppColors.primaryColor,
            size: 20.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  value,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showEditDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل معلومات المحل'),
        content: const Text('سيتم إضافة ميزة تعديل معلومات المحل قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: const Text('سيتم إضافة ميزة تغيير كلمة المرور قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showNotificationSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعدادات الإشعارات'),
        content: const Text('سيتم إضافة إعدادات الإشعارات قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showSupportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('المساعدة والدعم'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('للحصول على المساعدة، يرجى التواصل معنا:'),
            SizedBox(height: 16.h),
            Text('📞 ${AppConfig.supportPhone}'),
            SizedBox(height: 8.h),
            Text('📧 ${AppConfig.supportEmail}'),
            SizedBox(height: 8.h),
            Text('💬 ${AppConfig.supportWhatsApp}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              setState(() {
                _isLoading = true;
              });
              
              await _authService.logout();
              
              if (mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const LoginPage()),
                  (route) => false,
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorColor,
            ),
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }
}
