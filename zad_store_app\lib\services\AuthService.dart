import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Store.dart';
import '../utils/AppConfig.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  Store? _currentStore;
  String? _authToken;
  bool _isLoggedIn = false;

  // الحصول على المحل الحالي
  Store? get currentStore => _currentStore;
  String? get authToken => _authToken;
  bool get isLoggedIn => _isLoggedIn;

  // تهيئة الخدمة وتحميل بيانات المحل المحفوظة
  Future<void> initialize() async {
    await _loadStoredData();
  }

  // تحميل البيانات المحفوظة
  Future<void> _loadStoredData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // تحميل التوكن
      _authToken = prefs.getString(AppConfig.storeTokenKey);
      
      // تحميل بيانات المحل
      final storeJson = prefs.getString(AppConfig.storeDataKey);
      if (storeJson != null) {
        _currentStore = Store.fromJson(json.decode(storeJson));
        _isLoggedIn = true;
        print('تم تحميل بيانات المحل: ${_currentStore!.name}');
      }
    } catch (e) {
      print('خطأ في تحميل البيانات المحفوظة: $e');
    }
  }

  // حفظ البيانات محلياً
  Future<void> _saveData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      if (_authToken != null) {
        await prefs.setString(AppConfig.storeTokenKey, _authToken!);
      }
      
      if (_currentStore != null) {
        await prefs.setString(AppConfig.storeDataKey, json.encode(_currentStore!.toJson()));
      }
      
      print('تم حفظ بيانات المحل بنجاح');
    } catch (e) {
      print('خطأ في حفظ البيانات: $e');
    }
  }

  // تسجيل الدخول
  Future<AuthResult> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.authEndpoint)}/login'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'email': email,
          'password': password,
          'type': 'store', // تحديد نوع المستخدم كمحل
        }),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        
        // استخراج التوكن وبيانات المحل
        _authToken = responseData['token'];
        _currentStore = Store.fromJson(responseData['store']);
        _isLoggedIn = true;
        
        // حفظ البيانات محلياً
        await _saveData();
        
        return AuthResult.success('تم تسجيل الدخول بنجاح');
      } else if (response.statusCode == 401) {
        return AuthResult.error('بيانات الدخول غير صحيحة');
      } else if (response.statusCode == 403) {
        return AuthResult.error('المحل غير مفعل أو محظور');
      } else {
        return AuthResult.error('خطأ في الخادم، يرجى المحاولة لاحقاً');
      }
    } catch (e) {
      print('خطأ في تسجيل الدخول: $e');
      return AuthResult.error('خطأ في الاتصال بالخادم');
    }
  }

  // تسجيل محل جديد
  Future<AuthResult> register({
    required String storeName,
    required String ownerName,
    required String email,
    required String password,
    required String phone,
    required String address,
    required String category,
    required String description,
    required double latitude,
    required double longitude,
    required List<String> workingHours,
  }) async {
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.authEndpoint)}/register'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'store_name': storeName,
          'owner_name': ownerName,
          'email': email,
          'password': password,
          'phone': phone,
          'address': address,
          'category': category,
          'description': description,
          'latitude': latitude,
          'longitude': longitude,
          'working_hours': workingHours,
        }),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 201) {
        final responseData = json.decode(response.body);
        
        // استخراج التوكن وبيانات المحل
        _authToken = responseData['token'];
        _currentStore = Store.fromJson(responseData['store']);
        _isLoggedIn = true;
        
        // حفظ البيانات محلياً
        await _saveData();
        
        return AuthResult.success('تم تسجيل المحل بنجاح، في انتظار التحقق');
      } else if (response.statusCode == 409) {
        return AuthResult.error('البريد الإلكتروني مستخدم بالفعل');
      } else {
        final responseData = json.decode(response.body);
        return AuthResult.error(responseData['message'] ?? 'خطأ في التسجيل');
      }
    } catch (e) {
      print('خطأ في التسجيل: $e');
      return AuthResult.error('خطأ في الاتصال بالخادم');
    }
  }

  // تسجيل الخروج
  Future<void> logout() async {
    try {
      // إرسال طلب تسجيل الخروج للخادم
      if (_authToken != null) {
        await http.post(
          Uri.parse('${AppConfig.getFullUrl(AppConfig.authEndpoint)}/logout'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_authToken',
          },
        ).timeout(Duration(seconds: AppConfig.connectionTimeout));
      }
    } catch (e) {
      print('خطأ في تسجيل الخروج من الخادم: $e');
    }

    // مسح البيانات المحلية
    await _clearLocalData();
  }

  // مسح البيانات المحلية
  Future<void> _clearLocalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConfig.storeTokenKey);
      await prefs.remove(AppConfig.storeDataKey);
      await prefs.remove(AppConfig.ordersKey);
      await prefs.remove(AppConfig.dailyReportKey);
      
      _authToken = null;
      _currentStore = null;
      _isLoggedIn = false;
      
      print('تم مسح جميع البيانات المحلية');
    } catch (e) {
      print('خطأ في مسح البيانات المحلية: $e');
    }
  }

  // تحديث بيانات المحل
  Future<AuthResult> updateStoreInfo({
    String? storeName,
    String? description,
    String? phone,
    String? address,
    List<String>? workingHours,
  }) async {
    if (!_isLoggedIn || _currentStore == null) {
      return AuthResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      final updateData = <String, dynamic>{};
      if (storeName != null) updateData['store_name'] = storeName;
      if (description != null) updateData['description'] = description;
      if (phone != null) updateData['phone'] = phone;
      if (address != null) updateData['address'] = address;
      if (workingHours != null) updateData['working_hours'] = workingHours;

      final response = await http.patch(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.storeEndpoint)}/${_currentStore!.id}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_authToken',
        },
        body: json.encode(updateData),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        _currentStore = Store.fromJson(responseData['store']);
        await _saveData();
        
        return AuthResult.success('تم تحديث بيانات المحل بنجاح');
      } else {
        return AuthResult.error('خطأ في تحديث البيانات');
      }
    } catch (e) {
      print('خطأ في تحديث بيانات المحل: $e');
      return AuthResult.error('خطأ في الاتصال بالخادم');
    }
  }

  // تغيير كلمة المرور
  Future<AuthResult> changePassword(String currentPassword, String newPassword) async {
    if (!_isLoggedIn) {
      return AuthResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      final response = await http.patch(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.authEndpoint)}/change-password'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_authToken',
        },
        body: json.encode({
          'current_password': currentPassword,
          'new_password': newPassword,
        }),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        return AuthResult.success('تم تغيير كلمة المرور بنجاح');
      } else if (response.statusCode == 400) {
        return AuthResult.error('كلمة المرور الحالية غير صحيحة');
      } else {
        return AuthResult.error('خطأ في تغيير كلمة المرور');
      }
    } catch (e) {
      print('خطأ في تغيير كلمة المرور: $e');
      return AuthResult.error('خطأ في الاتصال بالخادم');
    }
  }

  // التحقق من صحة التوكن
  Future<bool> validateToken() async {
    if (_authToken == null) return false;

    try {
      final response = await http.get(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.authEndpoint)}/validate'),
        headers: {
          'Authorization': 'Bearer $_authToken',
        },
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      return response.statusCode == 200;
    } catch (e) {
      print('خطأ في التحقق من التوكن: $e');
      return false;
    }
  }

  // تحديث حالة المحل (مفتوح/مغلق)
  Future<AuthResult> updateStoreStatus(bool isActive) async {
    if (!_isLoggedIn || _currentStore == null) {
      return AuthResult.error('يجب تسجيل الدخول أولاً');
    }

    try {
      final response = await http.patch(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.storeEndpoint)}/${_currentStore!.id}/status'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_authToken',
        },
        body: json.encode({'is_active': isActive}),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        _currentStore = _currentStore!.copyWith(isActive: isActive);
        await _saveData();
        
        return AuthResult.success(isActive ? 'تم فتح المحل' : 'تم إغلاق المحل');
      } else {
        return AuthResult.error('خطأ في تحديث حالة المحل');
      }
    } catch (e) {
      print('خطأ في تحديث حالة المحل: $e');
      return AuthResult.error('خطأ في الاتصال بالخادم');
    }
  }
}

// نتيجة عمليات المصادقة
class AuthResult {
  final bool isSuccess;
  final String message;

  AuthResult._(this.isSuccess, this.message);

  factory AuthResult.success(String message) => AuthResult._(true, message);
  factory AuthResult.error(String message) => AuthResult._(false, message);
}
