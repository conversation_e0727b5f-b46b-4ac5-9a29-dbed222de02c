import 'dart:async';
import 'dart:convert';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/AppConfig.dart';
import '../utils/FirebaseConfig.dart';
import '../models/Order.dart';
import 'NotificationService.dart';
import 'OrderService.dart';

class FirebaseNotificationService {
  static final FirebaseNotificationService _instance = FirebaseNotificationService._internal();
  factory FirebaseNotificationService() => _instance;
  FirebaseNotificationService._internal();

  FirebaseMessaging? _messaging;
  FirebaseFirestore? _firestore;
  NotificationService? _notificationService;
  OrderService? _orderService;
  
  StreamSubscription<QuerySnapshot>? _notificationSubscription;
  String? _currentStoreId;
  String? _fcmToken;

  // تهيئة خدمة Firebase
  Future<void> initialize() async {
    try {
      // تهيئة Firebase
      await Firebase.initializeApp();
      
      _messaging = FirebaseMessaging.instance;
      _firestore = FirebaseFirestore.instance;
      _notificationService = NotificationService();
      _orderService = OrderService();

      // طلب الأذونات
      await _requestPermissions();
      
      // الحصول على FCM Token
      await _getFCMToken();
      
      // إعداد معالجات الرسائل
      await _setupMessageHandlers();
      
      print('تم تهيئة خدمة Firebase بنجاح');
    } catch (e) {
      print('خطأ في تهيئة Firebase: $e');
    }
  }

  // طلب الأذونات
  Future<void> _requestPermissions() async {
    if (_messaging == null) return;

    NotificationSettings settings = await _messaging!.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.authorized) {
      print('تم منح أذونات الإشعارات');
    } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
      print('تم منح أذونات الإشعارات المؤقتة');
    } else {
      print('تم رفض أذونات الإشعارات');
    }
  }

  // الحصول على FCM Token
  Future<void> _getFCMToken() async {
    if (_messaging == null) return;

    try {
      _fcmToken = await _messaging!.getToken();
      print('FCM Token: $_fcmToken');
      
      // حفظ التوكن محلياً
      if (_fcmToken != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', _fcmToken!);
      }
    } catch (e) {
      print('خطأ في الحصول على FCM Token: $e');
    }
  }

  // إعداد معالجات الرسائل
  Future<void> _setupMessageHandlers() async {
    if (_messaging == null) return;

    // معالج الرسائل في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // معالج الرسائل في الخلفية
    FirebaseMessaging.onMessageOpenedApp.listen(_handleBackgroundMessage);
    
    // معالج الرسائل عند إغلاق التطبيق
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  // معالج الرسائل في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('رسالة في المقدمة: ${message.messageId}');
    
    if (message.notification != null) {
      await _showLocalNotification(message);
    }
    
    // معالجة البيانات
    if (message.data.isNotEmpty) {
      await _handleNotificationData(message.data);
    }
  }

  // معالج الرسائل في الخلفية
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('رسالة في الخلفية: ${message.messageId}');
    
    // معالجة البيانات
    if (message.data.isNotEmpty) {
      await _handleNotificationData(message.data);
    }
  }

  // عرض الإشعار المحلي
  Future<void> _showLocalNotification(RemoteMessage message) async {
    if (_notificationService == null) return;

    final notification = message.notification;
    if (notification == null) return;

    // استخدام دالة الإشعار الموجودة
    await _notificationService!.showGeneralNotification(
      title: notification.title ?? 'إشعار جديد',
      body: notification.body ?? '',
      payload: json.encode(message.data),
    );
  }

  // معالجة بيانات الإشعار
  Future<void> _handleNotificationData(Map<String, dynamic> data) async {
    try {
      final type = data['type'];
      
      switch (type) {
        case 'new_order':
          await _handleNewOrderNotification(data);
          break;
        case 'order_update':
          await _handleOrderUpdateNotification(data);
          break;
        case 'store_message':
          await _handleStoreMessageNotification(data);
          break;
        default:
          print('نوع إشعار غير معروف: $type');
      }
    } catch (e) {
      print('خطأ في معالجة بيانات الإشعار: $e');
    }
  }

  // معالجة إشعار طلب جديد
  Future<void> _handleNewOrderNotification(Map<String, dynamic> data) async {
    try {
      final orderId = data['order_id'];
      if (orderId != null && _orderService != null) {
        // تحديث قائمة الطلبات
        await _orderService!.refreshOrders();
        
        // تشغيل صوت الإشعار
        await _notificationService?.playNotificationSound();
      }
    } catch (e) {
      print('خطأ في معالجة إشعار الطلب الجديد: $e');
    }
  }

  // معالجة إشعار تحديث الطلب
  Future<void> _handleOrderUpdateNotification(Map<String, dynamic> data) async {
    try {
      final orderId = data['order_id'];
      if (orderId != null && _orderService != null) {
        // تحديث الطلب المحدد
        await _orderService!.refreshOrder(orderId);
      }
    } catch (e) {
      print('خطأ في معالجة إشعار تحديث الطلب: $e');
    }
  }

  // معالجة إشعار رسالة المحل
  Future<void> _handleStoreMessageNotification(Map<String, dynamic> data) async {
    try {
      final message = data['message'];
      if (message != null) {
        print('رسالة للمحل: $message');
      }
    } catch (e) {
      print('خطأ في معالجة رسالة المحل: $e');
    }
  }

  // بدء الاستماع للإشعارات من Firestore
  Future<void> startListeningToStoreNotifications(String storeId) async {
    if (_firestore == null) return;

    _currentStoreId = storeId;

    try {
      // إلغاء الاستماع السابق
      await _notificationSubscription?.cancel();

      // بدء الاستماع الجديد
      _notificationSubscription = _firestore!
          .collection(FirebaseConfig.storeNotificationsCollection)
          .doc(storeId)
          .collection(FirebaseConfig.notificationsSubCollection)
          .where('read', isEqualTo: false)
          .orderBy('created_at', descending: true)
          .limit(FirebaseConfig.maxNotificationsLimit)
          .snapshots()
          .listen(_handleFirestoreNotifications);

      print('بدء الاستماع للإشعارات للمحل: $storeId');

      // الاشتراك في topic FCM للمحل
      await _subscribeToStoreTopic(storeId);

    } catch (e) {
      print('خطأ في بدء الاستماع للإشعارات: $e');
    }
  }

  // معالجة إشعارات Firestore
  Future<void> _handleFirestoreNotifications(QuerySnapshot snapshot) async {
    try {
      for (var change in snapshot.docChanges) {
        if (change.type == DocumentChangeType.added) {
          final data = change.doc.data() as Map<String, dynamic>;
          await _processFirestoreNotification(change.doc.id, data);
        }
      }
    } catch (e) {
      print('خطأ في معالجة إشعارات Firestore: $e');
    }
  }

  // معالجة إشعار Firestore
  Future<void> _processFirestoreNotification(String docId, Map<String, dynamic> data) async {
    try {
      final type = data['type'] ?? 'general';
      final title = data['title'] ?? 'إشعار جديد';
      final body = data['body'] ?? '';
      final orderData = data['order_data'];

      // عرض الإشعار المحلي
      await _notificationService?.showGeneralNotification(
        title: title,
        body: body,
        payload: json.encode(data),
      );

      // معالجة البيانات حسب النوع
      if (type == 'new_order' && orderData != null) {
        await _handleNewOrderFromFirestore(orderData);
      }

      // تحديد الإشعار كمقروء
      await _markNotificationAsRead(docId);
      
    } catch (e) {
      print('خطأ في معالجة إشعار Firestore: $e');
    }
  }

  // معالجة طلب جديد من Firestore
  Future<void> _handleNewOrderFromFirestore(Map<String, dynamic> orderData) async {
    try {
      final order = Order.fromJson(orderData);
      
      // إضافة الطلب للقائمة المحلية
      if (_orderService != null) {
        await _orderService!.addNewOrder(order);
      }
      
      // تشغيل صوت الإشعار
      await _notificationService?.playNotificationSound();
      
    } catch (e) {
      print('خطأ في معالجة الطلب الجديد من Firestore: $e');
    }
  }

  // تحديد الإشعار كمقروء
  Future<void> _markNotificationAsRead(String docId) async {
    if (_firestore == null || _currentStoreId == null) return;

    try {
      await _firestore!
          .collection(FirebaseConfig.storeNotificationsCollection)
          .doc(_currentStoreId!)
          .collection(FirebaseConfig.notificationsSubCollection)
          .doc(docId)
          .update({'read': true, 'read_at': DateTime.now().toIso8601String()});
    } catch (e) {
      print('خطأ في تحديد الإشعار كمقروء: $e');
    }
  }

  // الاشتراك في topic FCM للمحل
  Future<void> _subscribeToStoreTopic(String storeId) async {
    if (_messaging == null) return;

    try {
      final topicName = FirebaseConfig.getStoreTopicName(storeId);
      await _messaging!.subscribeToTopic(topicName);
      print('تم الاشتراك في topic: $topicName');
    } catch (e) {
      print('خطأ في الاشتراك في topic: $e');
    }
  }

  // إلغاء الاشتراك من topic FCM للمحل
  Future<void> _unsubscribeFromStoreTopic(String storeId) async {
    if (_messaging == null) return;

    try {
      final topicName = FirebaseConfig.getStoreTopicName(storeId);
      await _messaging!.unsubscribeFromTopic(topicName);
      print('تم إلغاء الاشتراك من topic: $topicName');
    } catch (e) {
      print('خطأ في إلغاء الاشتراك من topic: $e');
    }
  }

  // إيقاف الاستماع للإشعارات
  Future<void> stopListening() async {
    await _notificationSubscription?.cancel();
    _notificationSubscription = null;

    // إلغاء الاشتراك من topic إذا كان هناك محل محدد
    if (_currentStoreId != null) {
      await _unsubscribeFromStoreTopic(_currentStoreId!);
    }

    print('تم إيقاف الاستماع للإشعارات');
  }

  // الحصول على FCM Token
  String? get fcmToken => _fcmToken;

  // تنظيف الموارد
  Future<void> dispose() async {
    await stopListening();
  }
}

// معالج الرسائل في الخلفية (يجب أن يكون دالة عامة)
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('معالجة رسالة في الخلفية: ${message.messageId}');
}
