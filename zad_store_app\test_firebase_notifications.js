// ملف اختبار لإرسال إشعارات Firebase
// يمكن تشغيله باستخدام Node.js

const admin = require('firebase-admin');

// تهيئة Firebase Admin SDK
// يجب وضع ملف service account key في نفس المجلد
const serviceAccount = require('./path/to/serviceAccountKey.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://your-project-id.firebaseio.com'
});

const db = admin.firestore();
const messaging = admin.messaging();

// دالة لإضافة إشعار جديد في Firestore
async function addNotificationToFirestore() {
  try {
    const notification = {
      type: 'new_order',
      title: 'طلب جديد',
      body: 'لديك طلب جديد من أحمد محمد',
      order_data: {
        id: 'ORDER_' + Date.now(),
        customer_name: 'أحمد محمد',
        customer_phone: '*********',
        customer_address: 'شارع الزبيري، صنعاء',
        total: 25000,
        items: [
          {
            product_id: 'PROD_001',
            product_name: 'لحم غنم',
            quantity: 2,
            price: 12500,
            total: 25000
          }
        ],
        status: 'new',
        payment_method: 'cash',
        created_at: new Date().toISOString()
      },
      created_at: admin.firestore.FieldValue.serverTimestamp(),
      read: false,
      read_at: null
    };

    const docRef = await db
      .collection('store_notifications')
      .doc('ملحمة المعلم')
      .collection('notifications')
      .add(notification);

    console.log('تم إضافة الإشعار بنجاح:', docRef.id);
    return docRef.id;
  } catch (error) {
    console.error('خطأ في إضافة الإشعار:', error);
  }
}

// دالة لإرسال إشعار FCM
async function sendFCMNotification() {
  try {
    const message = {
      topic: 'store_ملحمة_المعلم',
      notification: {
        title: 'طلب جديد',
        body: 'لديك طلب جديد من أحمد محمد'
      },
      data: {
        type: 'new_order',
        order_id: 'ORDER_' + Date.now(),
        store_id: 'ملحمة المعلم'
      },
      android: {
        notification: {
          icon: 'ic_notification',
          color: '#C3243B',
          sound: 'default',
          channelId: 'zad_store_orders'
        }
      }
    };

    const response = await messaging.send(message);
    console.log('تم إرسال الإشعار بنجاح:', response);
    return response;
  } catch (error) {
    console.error('خطأ في إرسال الإشعار:', error);
  }
}

// دالة لإرسال إشعار لجهاز محدد
async function sendToDevice(fcmToken) {
  try {
    const message = {
      token: fcmToken,
      notification: {
        title: 'طلب جديد',
        body: 'لديك طلب جديد من سارة أحمد'
      },
      data: {
        type: 'new_order',
        order_id: 'ORDER_' + Date.now(),
        store_id: 'ملحمة المعلم'
      }
    };

    const response = await messaging.send(message);
    console.log('تم إرسال الإشعار للجهاز:', response);
    return response;
  } catch (error) {
    console.error('خطأ في إرسال الإشعار للجهاز:', error);
  }
}

// دالة لإضافة عدة إشعارات تجريبية
async function addMultipleTestNotifications() {
  const notifications = [
    {
      type: 'new_order',
      title: 'طلب جديد',
      body: 'طلب من محمد علي - 35,000 ريال',
      order_data: {
        id: 'ORDER_001',
        customer_name: 'محمد علي',
        total: 35000
      }
    },
    {
      type: 'order_update',
      title: 'تحديث الطلب',
      body: 'تم قبول طلب أحمد محمد',
      order_data: {
        id: 'ORDER_002',
        status: 'accepted'
      }
    },
    {
      type: 'store_message',
      title: 'رسالة من الإدارة',
      body: 'تم تحديث أسعار اللحوم'
    }
  ];

  for (const notif of notifications) {
    try {
      await db
        .collection('store_notifications')
        .doc('ملحمة المعلم')
        .collection('notifications')
        .add({
          ...notif,
          created_at: admin.firestore.FieldValue.serverTimestamp(),
          read: false,
          read_at: null
        });
      
      console.log('تم إضافة إشعار:', notif.title);
      
      // انتظار ثانية واحدة بين الإشعارات
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('خطأ في إضافة الإشعار:', error);
    }
  }
}

// دالة لحذف جميع الإشعارات (للاختبار)
async function clearAllNotifications() {
  try {
    const snapshot = await db
      .collection('store_notifications')
      .doc('ملحمة المعلم')
      .collection('notifications')
      .get();

    const batch = db.batch();
    snapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log('تم حذف جميع الإشعارات');
  } catch (error) {
    console.error('خطأ في حذف الإشعارات:', error);
  }
}

// تشغيل الاختبارات
async function runTests() {
  console.log('بدء اختبار الإشعارات...');
  
  // إضافة إشعار واحد
  await addNotificationToFirestore();
  
  // انتظار 3 ثوان
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // إضافة عدة إشعارات
  await addMultipleTestNotifications();
  
  // إرسال إشعار FCM
  await sendFCMNotification();
  
  console.log('انتهى الاختبار');
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  addNotificationToFirestore,
  sendFCMNotification,
  sendToDevice,
  addMultipleTestNotifications,
  clearAllNotifications
};
