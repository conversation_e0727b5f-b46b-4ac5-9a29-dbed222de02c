import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/AppColors.dart';
import '../utils/AppConfig.dart';
import '../services/AuthService.dart';
import '../services/OrderService.dart';
import '../models/Order.dart';
import '../models/Store.dart';
import '../widgets/OrderCard.dart';
import 'package:zad_store_app/pages/OrderDetailsPage.dart';
import 'OrdersPage.dart';
import 'DailyReportsPage.dart';
import 'ProfilePage.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  final AuthService _authService = AuthService();
  final OrderService _orderService = OrderService();
  
  late TabController _tabController;
  bool _isLoading = true;
  String _selectedFilter = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeData();
  }

  Future<void> _initializeData() async {
    try {
      final store = _authService.currentStore;
      if (store != null) {
        await _orderService.initialize(store);
      }
    } catch (e) {
      print('خطأ في تهيئة البيانات: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingWidget() : _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    final store = _authService.currentStore;
    
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            store?.name ?? 'زاد للمحلات',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.whiteColor,
              fontSize: 18.sp,
            ),
          ),
          Text(
            store?.statusText ?? '',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.whiteColor.withOpacity(0.8),
              fontSize: 12.sp,
            ),
          ),
        ],
      ),
      actions: [
        // حالة الاتصال
        Container(
          margin: EdgeInsets.only(right: 8.w),
          child: Icon(
            _orderService.isOnline ? Icons.wifi : Icons.wifi_off,
            color: _orderService.isOnline ? AppColors.successColor : AppColors.errorColor,
            size: 20.sp,
          ),
        ),
        
        // الإشعارات
        IconButton(
          onPressed: () {
            // فتح صفحة الإشعارات
          },
          icon: Stack(
            children: [
              Icon(
                Icons.notifications_outlined,
                color: AppColors.whiteColor,
                size: 24.sp,
              ),
              if (_orderService.getNewOrders().isNotEmpty)
                Positioned(
                  right: 0,
                  top: 0,
                  child: Container(
                    width: 8.w,
                    height: 8.w,
                    decoration: const BoxDecoration(
                      color: AppColors.errorColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
        ),
        
        // القائمة
        PopupMenuButton<String>(
          onSelected: _handleMenuSelection,
          icon: Icon(
            Icons.more_vert,
            color: AppColors.whiteColor,
            size: 24.sp,
          ),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'profile',
              child: Text('الملف الشخصي'),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Text('الإعدادات'),
            ),
            const PopupMenuItem(
              value: 'logout',
              child: Text('تسجيل الخروج'),
            ),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: AppColors.whiteColor,
        labelColor: AppColors.whiteColor,
        unselectedLabelColor: AppColors.whiteColor.withOpacity(0.7),
        tabs: [
          Tab(text: 'الكل (${_orderService.orders.length})'),
          Tab(text: 'جديد (${_orderService.getNewOrders().length})'),
          Tab(text: 'نشط (${_orderService.getActiveOrders().length})'),
          Tab(text: 'اليوم (${_orderService.getTodayOrders().length})'),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
      ),
    );
  }

  Widget _buildBody() {
    return Column(
      children: [
        // إحصائيات سريعة
        _buildQuickStats(),
        
        // قائمة الطلبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOrdersList(_orderService.orders),
              _buildOrdersList(_orderService.getNewOrders()),
              _buildOrdersList(_orderService.getActiveOrders()),
              _buildOrdersList(_orderService.getTodayOrders()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStats() {
    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Row(
        children: [
          Expanded(
            child: _buildStatItem(
              'طلبات اليوم',
              '${_orderService.getTodayOrdersCount()}',
              Icons.shopping_bag_outlined,
              AppColors.infoColor,
            ),
          ),
          Container(
            width: 1,
            height: 40.h,
            color: AppColors.dividerColor,
          ),
          Expanded(
            child: _buildStatItem(
              'مبيعات اليوم',
              AppConfig.formatCurrency(_orderService.getTodayRevenue()),
              Icons.attach_money,
              AppColors.successColor,
            ),
          ),
          Container(
            width: 1,
            height: 40.h,
            color: AppColors.dividerColor,
          ),
          Expanded(
            child: _buildStatItem(
              'طلبات جديدة',
              '${_orderService.getNewOrders().length}',
              Icons.fiber_new,
              AppColors.warningColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(
          icon,
          color: color,
          size: 24.sp,
        ),
        SizedBox(height: 4.h),
        Text(
          value,
          style: AppTextStyles.titleMedium.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          title,
          style: AppTextStyles.bodySmall.copyWith(
            fontSize: 10.sp,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildOrdersList(List<Order> orders) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64.sp,
              color: AppColors.textLight,
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد طلبات',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.textLight,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'ستظهر الطلبات هنا عند وصولها',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textLight,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshOrders,
      color: AppColors.primaryColor,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return OrderCard(
            order: order,
            onTap: () => _openOrderDetails(order),
            onAccept: () => _acceptOrder(order),
            onReject: () => _rejectOrder(order),
            onMarkReady: () => _markOrderReady(order),
            onMarkDelivered: () => _markOrderDelivered(order),
          );
        },
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const DailyReportsPage()),
        );
      },
      backgroundColor: AppColors.primaryColor,
      icon: Icon(Icons.analytics_outlined, size: 20.sp),
      label: Text(
        'التقارير',
        style: AppTextStyles.buttonText.copyWith(fontSize: 14.sp),
      ),
    );
  }

  Future<void> _refreshOrders() async {
    await _orderService.refreshOrders();
    if (mounted) {
      setState(() {});
    }
  }

  void _openOrderDetails(Order order) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => OrderDetailsPage(order: order),
      ),
    );
  }

  Future<void> _acceptOrder(Order order) async {
    final success = await _orderService.acceptOrder(order.id);
    if (success) {
      _showSuccessSnackBar('تم قبول الطلب بنجاح');
      setState(() {});
    } else {
      _showErrorSnackBar('فشل في قبول الطلب');
    }
  }

  Future<void> _rejectOrder(Order order) async {
    final reason = await _showRejectDialog();
    if (reason != null) {
      final success = await _orderService.rejectOrder(order.id, reason);
      if (success) {
        _showSuccessSnackBar('تم رفض الطلب');
        setState(() {});
      } else {
        _showErrorSnackBar('فشل في رفض الطلب');
      }
    }
  }

  Future<void> _markOrderReady(Order order) async {
    final success = await _orderService.markOrderReady(order.id);
    if (success) {
      _showSuccessSnackBar('تم تحديد الطلب كجاهز');
      setState(() {});
    } else {
      _showErrorSnackBar('فشل في تحديث حالة الطلب');
    }
  }

  Future<void> _markOrderDelivered(Order order) async {
    final success = await _orderService.markOrderDelivered(order.id);
    if (success) {
      _showSuccessSnackBar('تم تحديد الطلب كمسلم');
      setState(() {});
    } else {
      _showErrorSnackBar('فشل في تحديث حالة الطلب');
    }
  }

  Future<String?> _showRejectDialog() async {
    final controller = TextEditingController();
    
    return showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('رفض الطلب'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: 'سبب الرفض (اختياري)',
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(controller.text),
            child: const Text('رفض'),
          ),
        ],
      ),
    );
  }

  void _handleMenuSelection(String value) {
    switch (value) {
      case 'profile':
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const ProfilePage()),
        );
        break;
      case 'settings':
        // فتح صفحة الإعدادات
        break;
      case 'logout':
        _showLogoutDialog();
        break;
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _authService.logout();
              if (mounted) {
                Navigator.of(context).pushReplacementNamed('/login');
              }
            },
            child: const Text('تسجيل الخروج'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.successColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.errorColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
