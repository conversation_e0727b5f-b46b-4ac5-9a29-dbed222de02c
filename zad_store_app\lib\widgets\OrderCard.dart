import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../models/Order.dart';
import '../utils/AppColors.dart';
import '../utils/AppConfig.dart';

class OrderCard extends StatelessWidget {
  final Order order;
  final VoidCallback? onTap;
  final VoidCallback? onAccept;
  final VoidCallback? onReject;
  final VoidCallback? onMarkReady;
  final VoidCallback? onMarkDelivered;

  const OrderCard({
    super.key,
    required this.order,
    this.onTap,
    this.onAccept,
    this.onReject,
    this.onMarkReady,
    this.onMarkDelivered,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      decoration: AppCardStyles.defaultCard.copyWith(
        border: Border.all(
          color: _getStatusColor(),
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.r),
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              _buildCardHeader(),
              
              SizedBox(height: 12.h),
              
              // معلومات العميل
              _buildCustomerInfo(),
              
              SizedBox(height: 12.h),
              
              // عناصر الطلب
              _buildOrderItems(),
              
              SizedBox(height: 12.h),
              
              // المجموع والوقت
              _buildTotalAndTime(),
              
              SizedBox(height: 16.h),
              
              // أزرار الإجراءات
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // رقم الطلب
        Text(
          'طلب #${order.id.substring(0, 8)}',
          style: AppTextStyles.titleSmall.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        // حالة الطلب
        Container(
          padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
          decoration: BoxDecoration(
            color: _getStatusColor(),
            borderRadius: BorderRadius.circular(20.r),
          ),
          child: Text(
            order.statusText,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.whiteColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerInfo() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person_outline,
                size: 16.sp,
                color: AppColors.textSecondary,
              ),
              SizedBox(width: 8.w),
              Text(
                order.customerName,
                style: AppTextStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 4.h),
          
          Row(
            children: [
              Icon(
                Icons.phone_outlined,
                size: 16.sp,
                color: AppColors.textSecondary,
              ),
              SizedBox(width: 8.w),
              Text(
                order.customerPhone,
                style: AppTextStyles.bodyMedium,
                textDirection: TextDirection.ltr,
              ),
            ],
          ),
          
          SizedBox(height: 4.h),
          
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.location_on_outlined,
                size: 16.sp,
                color: AppColors.textSecondary,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  order.customerAddress,
                  style: AppTextStyles.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderItems() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'عناصر الطلب (${order.items.length})',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        
        SizedBox(height: 8.h),
        
        ...order.items.take(3).map((item) => Padding(
          padding: EdgeInsets.only(bottom: 4.h),
          child: Row(
            children: [
              Container(
                width: 4.w,
                height: 4.w,
                decoration: const BoxDecoration(
                  color: AppColors.textSecondary,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  '${item.quantity}x ${item.productName}',
                  style: AppTextStyles.bodySmall,
                ),
              ),
              Text(
                AppConfig.formatCurrency(item.total),
                style: AppTextStyles.bodySmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        )),
        
        if (order.items.length > 3)
          Padding(
            padding: EdgeInsets.only(top: 4.h),
            child: Text(
              'و ${order.items.length - 3} عناصر أخرى...',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTotalAndTime() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // المجموع
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المجموع الكلي',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              AppConfig.formatCurrency(order.total),
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        
        // الوقت
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              'منذ',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            Text(
              order.formattedTimeSinceCreated,
              style: AppTextStyles.bodyMedium.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    final buttons = <Widget>[];

    if (order.canAccept && onAccept != null) {
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onAccept,
            style: AppButtonStyles.successButton,
            icon: Icon(Icons.check, size: 16.sp),
            label: const Text('قبول'),
          ),
        ),
      );
      
      if (onReject != null) {
        buttons.add(SizedBox(width: 8.w));
        buttons.add(
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onReject,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.errorColor,
                foregroundColor: AppColors.whiteColor,
              ),
              icon: Icon(Icons.close, size: 16.sp),
              label: const Text('رفض'),
            ),
          ),
        );
      }
    } else if (order.canMarkReady && onMarkReady != null) {
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onMarkReady,
            style: AppButtonStyles.successButton,
            icon: Icon(Icons.restaurant, size: 16.sp),
            label: const Text('جاهز'),
          ),
        ),
      );
    } else if (order.canMarkDelivered && onMarkDelivered != null) {
      buttons.add(
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onMarkDelivered,
            style: AppButtonStyles.successButton,
            icon: Icon(Icons.delivery_dining, size: 16.sp),
            label: const Text('تم التسليم'),
          ),
        ),
      );
    }

    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(children: buttons);
  }

  Color _getStatusColor() {
    switch (order.status) {
      case 'new':
        return AppColors.newOrderColor;
      case 'accepted':
      case 'preparing':
        return AppColors.processingColor;
      case 'ready':
        return AppColors.readyColor;
      case 'delivered':
        return AppColors.deliveredColor;
      case 'cancelled':
        return AppColors.cancelledColor;
      default:
        return AppColors.textSecondary;
    }
  }
}
