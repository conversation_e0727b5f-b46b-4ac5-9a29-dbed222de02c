import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import '../utils/AppColors.dart';
import '../utils/AppConfig.dart';
import '../services/OrderService.dart';
import '../models/Order.dart';

class DailyReportsPage extends StatefulWidget {
  const DailyReportsPage({super.key});

  @override
  State<DailyReportsPage> createState() => _DailyReportsPageState();
}

class _DailyReportsPageState extends State<DailyReportsPage> {
  final OrderService _orderService = OrderService();
  DateTime _selectedDate = DateTime.now();
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      appBar: _buildAppBar(),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // منتقي التاريخ
            _buildDatePicker(),
            
            SizedBox(height: 16.h),
            
            // الإحصائيات الرئيسية
            _buildMainStats(),
            
            SizedBox(height: 16.h),
            
            // إحصائيات الطلبات حسب الحالة
            _buildOrderStatusStats(),
            
            SizedBox(height: 16.h),
            
            // الإيرادات والمبيعات
            _buildRevenueStats(),
            
            SizedBox(height: 16.h),
            
            // أفضل المنتجات
            _buildTopProducts(),
            
            SizedBox(height: 16.h),
            
            // قائمة الطلبات
            _buildOrdersList(),
          ],
        ),
      ),
      floatingActionButton: _buildExportButton(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('التقارير اليومية'),
      actions: [
        IconButton(
          onPressed: _refreshData,
          icon: Icon(Icons.refresh, size: 24.sp),
        ),
      ],
    );
  }

  Widget _buildDatePicker() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Row(
        children: [
          Icon(
            Icons.calendar_today,
            color: AppColors.primaryColor,
            size: 24.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'التاريخ المحدد',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
                Text(
                  DateFormat('EEEE، d MMMM yyyy', 'ar').format(_selectedDate),
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: _selectDate,
            style: AppButtonStyles.primaryButton,
            child: const Text('تغيير'),
          ),
        ],
      ),
    );
  }

  Widget _buildMainStats() {
    final dayOrders = _getOrdersForDate(_selectedDate);
    final totalRevenue = _calculateRevenue(dayOrders);
    final completedOrders = dayOrders.where((o) => o.status == 'delivered').length;
    
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.gradientCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإحصائيات الرئيسية',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الطلبات',
                  '${dayOrders.length}',
                  Icons.shopping_bag_outlined,
                  AppColors.infoColor,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  'الطلبات المكتملة',
                  '$completedOrders',
                  Icons.check_circle_outline,
                  AppColors.successColor,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 12.h),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الإيرادات',
                  AppConfig.formatCurrency(totalRevenue),
                  Icons.attach_money,
                  AppColors.primaryColor,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildStatCard(
                  'متوسط قيمة الطلب',
                  completedOrders > 0 
                      ? AppConfig.formatCurrency(totalRevenue / completedOrders)
                      : AppConfig.formatCurrency(0),
                  Icons.trending_up,
                  AppColors.warningColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24.sp,
          ),
          SizedBox(height: 8.h),
          Text(
            value,
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderStatusStats() {
    final dayOrders = _getOrdersForDate(_selectedDate);
    final statusCounts = <String, int>{};
    
    for (final order in dayOrders) {
      statusCounts[order.status] = (statusCounts[order.status] ?? 0) + 1;
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الطلبات حسب الحالة',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          ...AppConfig.orderStatusTexts.entries.map((entry) {
            final status = entry.key;
            final statusText = entry.value;
            final count = statusCounts[status] ?? 0;
            final percentage = dayOrders.isNotEmpty ? (count / dayOrders.length * 100) : 0;
            
            return _buildStatusRow(statusText, count, percentage.toDouble(), _getStatusColor(status));
          }),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String status, int count, double percentage, Color color) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          Container(
            width: 12.w,
            height: 12.w,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              status,
              style: AppTextStyles.bodyMedium,
            ),
          ),
          Text(
            '$count',
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            '(${percentage.toStringAsFixed(1)}%)',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueStats() {
    final dayOrders = _getOrdersForDate(_selectedDate);
    final completedOrders = dayOrders.where((o) => o.status == 'delivered').toList();
    
    final totalRevenue = _calculateRevenue(completedOrders);
    final totalSubtotal = completedOrders.fold(0.0, (sum, order) => sum + order.subtotal);
    final totalDeliveryFees = completedOrders.fold(0.0, (sum, order) => sum + order.deliveryFee);
    final totalDiscounts = completedOrders.fold(0.0, (sum, order) => sum + order.discount);

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل الإيرادات',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          _buildRevenueRow('إجمالي المبيعات', totalSubtotal),
          _buildRevenueRow('رسوم التوصيل', totalDeliveryFees),
          _buildRevenueRow('الخصومات', totalDiscounts, isDiscount: true),
          
          Divider(height: 20.h),
          
          _buildRevenueRow('صافي الإيرادات', totalRevenue, isTotal: true),
        ],
      ),
    );
  }

  Widget _buildRevenueRow(String label, double amount, {bool isDiscount = false, bool isTotal = false}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: isTotal 
                ? AppTextStyles.titleSmall.copyWith(fontWeight: FontWeight.bold)
                : AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
          ),
          Text(
            isDiscount ? '-${AppConfig.formatCurrency(amount)}' : AppConfig.formatCurrency(amount),
            style: isTotal 
                ? AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.primaryColor,
                  )
                : AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isDiscount ? AppColors.errorColor : null,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopProducts() {
    final dayOrders = _getOrdersForDate(_selectedDate);
    final productStats = <String, Map<String, dynamic>>{};
    
    for (final order in dayOrders) {
      if (order.status == 'delivered') {
        for (final item in order.items) {
          if (productStats.containsKey(item.productName)) {
            productStats[item.productName]!['quantity'] += item.quantity;
            productStats[item.productName]!['revenue'] += item.total;
          } else {
            productStats[item.productName] = {
              'quantity': item.quantity,
              'revenue': item.total,
            };
          }
        }
      }
    }
    
    final sortedProducts = productStats.entries.toList()
      ..sort((a, b) => b.value['quantity'].compareTo(a.value['quantity']));

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أفضل المنتجات مبيعاً',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          if (sortedProducts.isEmpty)
            Center(
              child: Text(
                'لا توجد مبيعات في هذا التاريخ',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            )
          else
            ...sortedProducts.take(5).map((entry) {
              final productName = entry.key;
              final quantity = entry.value['quantity'];
              final revenue = entry.value['revenue'];
              
              return _buildProductRow(productName, quantity, revenue);
            }),
        ],
      ),
    );
  }

  Widget _buildProductRow(String productName, int quantity, double revenue) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              color: AppColors.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Icon(
              Icons.fastfood,
              color: AppColors.primaryColor,
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  productName,
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'الكمية: $quantity',
                  style: AppTextStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            AppConfig.formatCurrency(revenue),
            style: AppTextStyles.bodyMedium.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    final dayOrders = _getOrdersForDate(_selectedDate);
    
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: AppCardStyles.defaultCard,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طلبات اليوم (${dayOrders.length})',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          
          SizedBox(height: 16.h),
          
          if (dayOrders.isEmpty)
            Center(
              child: Text(
                'لا توجد طلبات في هذا التاريخ',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            )
          else
            ...dayOrders.take(10).map((order) => _buildOrderSummaryRow(order)),
          
          if (dayOrders.length > 10) ...[
            SizedBox(height: 8.h),
            Center(
              child: TextButton(
                onPressed: () {
                  // فتح صفحة جميع الطلبات
                },
                child: Text('عرض جميع الطلبات (${dayOrders.length})'),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOrderSummaryRow(Order order) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.w,
            decoration: BoxDecoration(
              color: _getStatusColor(order.status),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              '#${order.id.substring(0, 8)} - ${order.customerName}',
              style: AppTextStyles.bodySmall,
            ),
          ),
          Text(
            AppConfig.formatCurrency(order.total),
            style: AppTextStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportButton() {
    return FloatingActionButton.extended(
      onPressed: _exportReport,
      backgroundColor: AppColors.primaryColor,
      icon: Icon(Icons.file_download, size: 20.sp),
      label: Text(
        'تصدير',
        style: AppTextStyles.buttonText.copyWith(fontSize: 14.sp),
      ),
    );
  }

  List<Order> _getOrdersForDate(DateTime date) {
    return _orderService.orders.where((order) {
      return order.createdAt.year == date.year &&
             order.createdAt.month == date.month &&
             order.createdAt.day == date.day;
    }).toList();
  }

  double _calculateRevenue(List<Order> orders) {
    return orders
        .where((order) => order.status == 'delivered')
        .fold(0.0, (sum, order) => sum + order.total);
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'new':
        return AppColors.newOrderColor;
      case 'accepted':
      case 'preparing':
        return AppColors.processingColor;
      case 'ready':
        return AppColors.readyColor;
      case 'delivered':
        return AppColors.deliveredColor;
      case 'cancelled':
        return AppColors.cancelledColor;
      default:
        return AppColors.textSecondary;
    }
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      locale: const Locale('ar', 'YE'),
    );
    
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _refreshData() {
    setState(() {});
  }

  void _exportReport() {
    // يمكن إضافة منطق تصدير التقرير هنا
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تصدير التقرير'),
        content: const Text('سيتم إضافة ميزة تصدير التقارير قريباً'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
