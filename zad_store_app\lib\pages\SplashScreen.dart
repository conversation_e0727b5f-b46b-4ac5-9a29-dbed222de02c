import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../utils/AppColors.dart';
import '../utils/AppConfig.dart';
import '../services/AuthService.dart';
import '../services/NotificationService.dart';
import '../services/BackgroundService.dart';
import 'LoginPage.dart';
import 'HomePage.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _textController;
  late Animation<double> _logoAnimation;
  late Animation<double> _textAnimation;
  late Animation<Offset> _slideAnimation;

  final AuthService _authService = AuthService();
  final NotificationService _notificationService = NotificationService();
  final BackgroundService _backgroundService = BackgroundService();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    // تحكم في الرسوم المتحركة للشعار
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // تحكم في الرسوم المتحركة للنص
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // رسوم متحركة للشعار (تكبير وتصغير)
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // رسوم متحركة للنص (ظهور تدريجي)
    _textAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeInOut,
    ));

    // رسوم متحركة للانزلاق
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));

    // بدء الرسوم المتحركة
    _logoController.forward();
    
    // تأخير بدء رسوم النص
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _textController.forward();
      }
    });
  }

  Future<void> _initializeApp() async {
    try {
      // تهيئة الخدمات
      await _authService.initialize();
      await _notificationService.initialize();
      await _backgroundService.initialize();

      // انتظار مدة العرض
      await Future.delayed(Duration(seconds: AppConfig.splashDuration));

      // التحقق من حالة تسجيل الدخول
      await _checkAuthStatus();
    } catch (e) {
      print('خطأ في تهيئة التطبيق: $e');
      // في حالة الخطأ، الانتقال إلى صفحة تسجيل الدخول
      _navigateToLogin();
    }
  }

  Future<void> _checkAuthStatus() async {
    if (_authService.isLoggedIn) {
      // التحقق من صحة التوكن
      final isValidToken = await _authService.validateToken();
      
      if (isValidToken) {
        // بدء خدمة المراقبة في الخلفية
        await _backgroundService.startOrderMonitoring();
        _navigateToHome();
      } else {
        // التوكن غير صالح - تسجيل الخروج والانتقال لتسجيل الدخول
        await _authService.logout();
        _navigateToLogin();
      }
    } else {
      _navigateToLogin();
    }
  }

  void _navigateToHome() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const HomePage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  void _navigateToLogin() {
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const LoginPage(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(animation),
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _textController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppColors.primaryGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // الجزء العلوي - الشعار والنص
              Expanded(
                flex: 3,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // الشعار
                      AnimatedBuilder(
                        animation: _logoAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _logoAnimation.value,
                            child: Container(
                              width: 120.w,
                              height: 120.w,
                              decoration: BoxDecoration(
                                color: AppColors.whiteColor,
                                borderRadius: BorderRadius.circular(30.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 10),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.store,
                                size: 60.sp,
                                color: AppColors.primaryColor,
                              ),
                            ),
                          );
                        },
                      ),
                      
                      SizedBox(height: 30.h),
                      
                      // النص الرئيسي
                      SlideTransition(
                        position: _slideAnimation,
                        child: FadeTransition(
                          opacity: _textAnimation,
                          child: Column(
                            children: [
                              Text(
                                AppConfig.appName,
                                style: AppTextStyles.titleLarge.copyWith(
                                  color: AppColors.whiteColor,
                                  fontSize: 32.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              
                              SizedBox(height: 8.h),
                              
                              Text(
                                AppConfig.appDescription,
                                style: AppTextStyles.bodyMedium.copyWith(
                                  color: AppColors.whiteColor.withOpacity(0.9),
                                  fontSize: 16.sp,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // الجزء السفلي - مؤشر التحميل والنسخة
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // مؤشر التحميل
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(AppColors.whiteColor),
                      strokeWidth: 3,
                    ),
                    
                    SizedBox(height: 20.h),
                    
                    // نص التحميل
                    Text(
                      'جاري التحميل...',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.whiteColor.withOpacity(0.8),
                        fontSize: 14.sp,
                      ),
                    ),
                    
                    SizedBox(height: 30.h),
                    
                    // رقم النسخة
                    Text(
                      'الإصدار ${AppConfig.appVersion}',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.whiteColor.withOpacity(0.6),
                        fontSize: 12.sp,
                      ),
                    ),
                    
                    SizedBox(height: 20.h),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
