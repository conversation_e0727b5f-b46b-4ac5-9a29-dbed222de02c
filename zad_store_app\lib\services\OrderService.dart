import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/Order.dart';
import '../models/Store.dart';
import '../utils/AppConfig.dart';
import 'NotificationService.dart';

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final NotificationService _notificationService = NotificationService();
  
  List<Order> _orders = [];
  Store? _currentStore;
  bool _isOnline = true;

  // الحصول على قائمة الطلبات
  List<Order> get orders => _orders;
  Store? get currentStore => _currentStore;
  bool get isOnline => _isOnline;

  // تهيئة الخدمة
  Future<void> initialize(Store store) async {
    _currentStore = store;
    await _loadCachedOrders();
    await _syncOrders();
  }

  // تحميل الطلبات المحفوظة محلياً
  Future<void> _loadCachedOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = prefs.getString(AppConfig.ordersKey);
      
      if (ordersJson != null) {
        final List<dynamic> ordersList = json.decode(ordersJson);
        _orders = ordersList.map((json) => Order.fromJson(json)).toList();
        print('تم تحميل ${_orders.length} طلب من التخزين المحلي');
      }
    } catch (e) {
      print('خطأ في تحميل الطلبات المحفوظة: $e');
    }
  }

  // حفظ الطلبات محلياً
  Future<void> _saveCachedOrders() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final ordersJson = json.encode(_orders.map((order) => order.toJson()).toList());
      await prefs.setString(AppConfig.ordersKey, ordersJson);
      print('تم حفظ ${_orders.length} طلب في التخزين المحلي');
    } catch (e) {
      print('خطأ في حفظ الطلبات: $e');
    }
  }

  // مزامنة الطلبات مع الخادم
  Future<void> _syncOrders() async {
    if (_currentStore == null) return;

    try {
      final response = await http.get(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.ordersEndpoint)}/${_currentStore!.id}'),
        headers: await _getHeaders(),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        final List<dynamic> ordersData = json.decode(response.body);
        final List<Order> serverOrders = ordersData.map((json) => Order.fromJson(json)).toList();
        
        // التحقق من الطلبات الجديدة
        await _checkForNewOrders(serverOrders);
        
        _orders = serverOrders;
        await _saveCachedOrders();
        _isOnline = true;
        
        print('تم مزامنة ${_orders.length} طلب من الخادم');
      } else {
        print('خطأ في مزامنة الطلبات: ${response.statusCode}');
        _isOnline = false;
      }
    } catch (e) {
      print('خطأ في الاتصال بالخادم: $e');
      _isOnline = false;
    }
  }

  // التحقق من الطلبات الجديدة وإرسال إشعارات
  Future<void> _checkForNewOrders(List<Order> serverOrders) async {
    final existingOrderIds = _orders.map((order) => order.id).toSet();
    
    for (Order order in serverOrders) {
      if (!existingOrderIds.contains(order.id) && order.status == 'new') {
        // طلب جديد - إرسال إشعار
        await _notificationService.showNewOrderNotification(order);
      }
    }
  }

  // الحصول على headers للطلبات
  Future<Map<String, String>> _getHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(AppConfig.storeTokenKey) ?? '';
    
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // قبول طلب
  Future<bool> acceptOrder(String orderId) async {
    try {
      final response = await http.patch(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.ordersEndpoint)}/$orderId/accept'),
        headers: await _getHeaders(),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        // تحديث الطلب محلياً
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          _orders[orderIndex] = _orders[orderIndex].copyWith(
            status: 'accepted',
            acceptedAt: DateTime.now(),
          );
          await _saveCachedOrders();
          
          // إرسال إشعار تحديث الحالة
          await _notificationService.showOrderStatusUpdateNotification(_orders[orderIndex]);
        }
        
        return true;
      } else {
        print('خطأ في قبول الطلب: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('خطأ في قبول الطلب: $e');
      return false;
    }
  }

  // رفض طلب
  Future<bool> rejectOrder(String orderId, String reason) async {
    try {
      final response = await http.patch(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.ordersEndpoint)}/$orderId/reject'),
        headers: await _getHeaders(),
        body: json.encode({'reason': reason}),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        // تحديث الطلب محلياً
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          _orders[orderIndex] = _orders[orderIndex].copyWith(
            status: 'cancelled',
            cancelReason: reason,
          );
          await _saveCachedOrders();
        }
        
        return true;
      } else {
        print('خطأ في رفض الطلب: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('خطأ في رفض الطلب: $e');
      return false;
    }
  }

  // تحديد الطلب كجاهز
  Future<bool> markOrderReady(String orderId) async {
    try {
      final response = await http.patch(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.ordersEndpoint)}/$orderId/ready'),
        headers: await _getHeaders(),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        // تحديث الطلب محلياً
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          _orders[orderIndex] = _orders[orderIndex].copyWith(
            status: 'ready',
            readyAt: DateTime.now(),
          );
          await _saveCachedOrders();
          
          // إرسال إشعار تحديث الحالة
          await _notificationService.showOrderStatusUpdateNotification(_orders[orderIndex]);
        }
        
        return true;
      } else {
        print('خطأ في تحديد الطلب كجاهز: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('خطأ في تحديد الطلب كجاهز: $e');
      return false;
    }
  }

  // تحديد الطلب كمسلم
  Future<bool> markOrderDelivered(String orderId) async {
    try {
      final response = await http.patch(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.ordersEndpoint)}/$orderId/delivered'),
        headers: await _getHeaders(),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        // تحديث الطلب محلياً
        final orderIndex = _orders.indexWhere((order) => order.id == orderId);
        if (orderIndex != -1) {
          _orders[orderIndex] = _orders[orderIndex].copyWith(
            status: 'delivered',
            deliveredAt: DateTime.now(),
          );
          await _saveCachedOrders();
        }
        
        return true;
      } else {
        print('خطأ في تحديد الطلب كمسلم: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('خطأ في تحديد الطلب كمسلم: $e');
      return false;
    }
  }

  // الحصول على طلبات اليوم
  List<Order> getTodayOrders() {
    final today = DateTime.now();
    return _orders.where((order) {
      return order.createdAt.year == today.year &&
             order.createdAt.month == today.month &&
             order.createdAt.day == today.day;
    }).toList();
  }

  // الحصول على طلبات حسب الحالة
  List<Order> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  // الحصول على إجمالي مبيعات اليوم
  double getTodayRevenue() {
    final todayOrders = getTodayOrders();
    return todayOrders
        .where((order) => order.status == 'delivered')
        .fold(0.0, (sum, order) => sum + order.total);
  }

  // الحصول على عدد طلبات اليوم
  int getTodayOrdersCount() {
    return getTodayOrders().length;
  }

  // الحصول على الطلبات الجديدة
  List<Order> getNewOrders() {
    return getOrdersByStatus('new');
  }

  // الحصول على الطلبات قيد التحضير
  List<Order> getActiveOrders() {
    return _orders.where((order) => 
        ['accepted', 'preparing', 'ready'].contains(order.status)
    ).toList();
  }

  // تحديث حالة الاتصال
  void updateConnectionStatus(bool isOnline) {
    _isOnline = isOnline;
    if (isOnline) {
      _syncOrders();
    }
  }

  // إعادة تحميل الطلبات
  Future<void> refreshOrders() async {
    await _syncOrders();
  }

  // البحث في الطلبات
  List<Order> searchOrders(String query) {
    if (query.isEmpty) return _orders;

    return _orders.where((order) {
      return order.customerName.toLowerCase().contains(query.toLowerCase()) ||
             order.customerPhone.contains(query) ||
             order.id.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // تحديث طلب محدد
  Future<void> refreshOrder(String orderId) async {
    if (_currentStore == null) return;

    try {
      final response = await http.get(
        Uri.parse('${AppConfig.getFullUrl(AppConfig.ordersEndpoint)}/$orderId'),
        headers: await _getHeaders(),
      ).timeout(Duration(seconds: AppConfig.connectionTimeout));

      if (response.statusCode == 200) {
        final orderData = json.decode(response.body);
        final updatedOrder = Order.fromJson(orderData);

        // تحديث الطلب في القائمة
        final index = _orders.indexWhere((order) => order.id == orderId);
        if (index != -1) {
          _orders[index] = updatedOrder;
          await _saveCachedOrders();
          print('تم تحديث الطلب: $orderId');
        }
      }
    } catch (e) {
      print('خطأ في تحديث الطلب $orderId: $e');
    }
  }

  // إضافة طلب جديد للقائمة
  Future<void> addNewOrder(Order order) async {
    // التحقق من عدم وجود الطلب مسبقاً
    final existingIndex = _orders.indexWhere((o) => o.id == order.id);
    if (existingIndex == -1) {
      _orders.insert(0, order); // إضافة في المقدمة
      await _saveCachedOrders();
      print('تم إضافة طلب جديد: ${order.id}');
    }
  }
}
