import 'package:flutter/material.dart';

class AppColors {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFFC3243B); // أحمر زاد
  static const Color secondaryColor = Color(0xFFE8F5E8); // أخضر فاتح
  static const Color accentColor = Color(0xFF4CAF50); // أخضر للحالات الإيجابية
  
  // ألوان الخلفية
  static const Color backgroundColor = Color(0xFFF8F9FA);
  static const Color whiteColor = Color(0xFFFFFFFF);
  static const Color cardColor = Color(0xFFFFFFFF);
  
  // ألوان النصوص
  static const Color textPrimary = Color(0xFF2C3E50);
  static const Color textSecondary = Color(0xFF7F8C8D);
  static const Color textLight = Color(0xFFBDC3C7);
  
  // ألوان الحالات
  static const Color successColor = Color(0xFF27AE60); // أخضر للنجاح
  static const Color warningColor = Color(0xFFF39C12); // برتقالي للتحذير
  static const Color errorColor = Color(0xFFE74C3C); // أحمر للخطأ
  static const Color infoColor = Color(0xFF3498DB); // أزرق للمعلومات
  
  // ألوان حالات الطلبات
  static const Color newOrderColor = Color(0xFF3498DB); // أزرق للطلبات الجديدة
  static const Color processingColor = Color(0xFFF39C12); // برتقالي للمعالجة
  static const Color readyColor = Color(0xFF27AE60); // أخضر للجاهز
  static const Color deliveredColor = Color(0xFF95A5A6); // رمادي للمسلم
  static const Color cancelledColor = Color(0xFFE74C3C); // أحمر للملغي
  
  // ألوان الحدود
  static const Color borderColor = Color(0xFFE1E8ED);
  static const Color dividerColor = Color(0xFFECF0F1);
  
  // ألوان الظلال
  static const Color shadowColor = Color(0x1A000000);
  
  // تدرجات لونية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [Color(0xFFC3243B), Color(0xFFE74C3C)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [Color(0xFF27AE60), Color(0xFF2ECC71)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient cardGradient = LinearGradient(
    colors: [Color(0xFFFFFFFF), Color(0xFFF8F9FA)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}

// أنماط النصوص
class AppTextStyles {
  static const TextStyle titleLarge = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: AppColors.textPrimary,
    fontFamily: 'Cairo',
  );
  
  static const TextStyle titleMedium = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    fontFamily: 'Cairo',
  );
  
  static const TextStyle titleSmall = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.textPrimary,
    fontFamily: 'Cairo',
  );
  
  static const TextStyle bodyLarge = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.normal,
    color: AppColors.textPrimary,
    fontFamily: 'Cairo',
  );
  
  static const TextStyle bodyMedium = TextStyle(
    fontSize: 14,
    fontWeight: FontWeight.normal,
    color: AppColors.textSecondary,
    fontFamily: 'Cairo',
  );
  
  static const TextStyle bodySmall = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.normal,
    color: AppColors.textLight,
    fontFamily: 'Cairo',
  );
  
  static const TextStyle buttonText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: AppColors.whiteColor,
    fontFamily: 'Cairo',
  );
}

// أنماط الأزرار
class AppButtonStyles {
  static ButtonStyle primaryButton = ElevatedButton.styleFrom(
    backgroundColor: AppColors.primaryColor,
    foregroundColor: AppColors.whiteColor,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    elevation: 2,
  );
  
  static ButtonStyle successButton = ElevatedButton.styleFrom(
    backgroundColor: AppColors.successColor,
    foregroundColor: AppColors.whiteColor,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    elevation: 2,
  );
  
  static ButtonStyle warningButton = ElevatedButton.styleFrom(
    backgroundColor: AppColors.warningColor,
    foregroundColor: AppColors.whiteColor,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12),
    ),
    elevation: 2,
  );
}

// أنماط البطاقات
class AppCardStyles {
  static BoxDecoration defaultCard = BoxDecoration(
    color: AppColors.cardColor,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowColor,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );
  
  static BoxDecoration gradientCard = BoxDecoration(
    gradient: AppColors.cardGradient,
    borderRadius: BorderRadius.circular(12),
    boxShadow: [
      BoxShadow(
        color: AppColors.shadowColor,
        blurRadius: 8,
        offset: const Offset(0, 2),
      ),
    ],
  );
}
