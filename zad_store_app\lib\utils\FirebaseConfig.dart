class FirebaseConfig {
  // إعدادات Firebase للمحل
  static const String storeNotificationsCollection = 'store_notifications';
  static const String notificationsSubCollection = 'notifications';
  
  // معرف المحل الافتراضي (يجب تحديثه حسب المحل المسجل دخوله)
  static const String defaultStoreId = 'ملحمة المعلم';
  
  // أنواع الإشعارات
  static const String notificationTypeNewOrder = 'new_order';
  static const String notificationTypeOrderUpdate = 'order_update';
  static const String notificationTypeStoreMessage = 'store_message';
  static const String notificationTypeGeneral = 'general';
  
  // حالات الإشعارات
  static const String notificationStatusUnread = 'unread';
  static const String notificationStatusRead = 'read';
  
  // إعدادات FCM
  static const String fcmTopicPrefix = 'store_';
  static const String fcmTopicAllStores = 'all_stores';
  
  // الحصول على topic FCM للمحل
  static String getStoreTopicName(String storeId) {
    return '$fcmTopicPrefix${storeId.replaceAll(' ', '_')}';
  }
  
  // الحصول على مسار مجموعة إشعارات المحل
  static String getStoreNotificationsPath(String storeId) {
    return '$storeNotificationsCollection/$storeId/$notificationsSubCollection';
  }
  
  // إعدادات الاستعلام
  static const int maxNotificationsLimit = 50;
  static const int notificationRetentionDays = 30;
  
  // رسائل النظام
  static const Map<String, String> firebaseMessages = {
    'connection_success': 'تم الاتصال بـ Firebase بنجاح',
    'connection_failed': 'فشل في الاتصال بـ Firebase',
    'notification_received': 'تم استلام إشعار جديد',
    'notification_processed': 'تم معالجة الإشعار',
    'subscription_success': 'تم الاشتراك في الإشعارات',
    'subscription_failed': 'فشل في الاشتراك في الإشعارات',
  };
}
