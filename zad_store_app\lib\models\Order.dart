import 'Product.dart';

class Order {
  final String id;
  final String customerId;
  final String customerName;
  final String customerPhone;
  final String customerAddress;
  final String storeId;
  final List<OrderItem> items;
  final double subtotal;
  final double deliveryFee;
  final double discount;
  final double total;
  final String status;
  final String paymentMethod;
  final String paymentStatus;
  final DateTime createdAt;
  final DateTime? acceptedAt;
  final DateTime? readyAt;
  final DateTime? deliveredAt;
  final String? notes;
  final String? cancelReason;
  final Map<String, dynamic>? metadata;

  Order({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.customerPhone,
    required this.customerAddress,
    required this.storeId,
    required this.items,
    required this.subtotal,
    required this.deliveryFee,
    required this.discount,
    required this.total,
    required this.status,
    required this.paymentMethod,
    required this.paymentStatus,
    required this.createdAt,
    this.acceptedAt,
    this.readyAt,
    this.deliveredAt,
    this.notes,
    this.cancelReason,
    this.metadata,
  });

  // تحويل من JSON
  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      id: json['id'] ?? '',
      customerId: json['customer_id'] ?? '',
      customerName: json['customer_name'] ?? '',
      customerPhone: json['customer_phone'] ?? '',
      customerAddress: json['customer_address'] ?? '',
      storeId: json['store_id'] ?? '',
      items: (json['items'] as List<dynamic>?)
          ?.map((item) => OrderItem.fromJson(item))
          .toList() ?? [],
      subtotal: (json['subtotal'] ?? 0.0).toDouble(),
      deliveryFee: (json['delivery_fee'] ?? 0.0).toDouble(),
      discount: (json['discount'] ?? 0.0).toDouble(),
      total: (json['total'] ?? 0.0).toDouble(),
      status: json['status'] ?? 'new',
      paymentMethod: json['payment_method'] ?? 'cash',
      paymentStatus: json['payment_status'] ?? 'pending',
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      acceptedAt: json['accepted_at'] != null ? DateTime.parse(json['accepted_at']) : null,
      readyAt: json['ready_at'] != null ? DateTime.parse(json['ready_at']) : null,
      deliveredAt: json['delivered_at'] != null ? DateTime.parse(json['delivered_at']) : null,
      notes: json['notes'],
      cancelReason: json['cancel_reason'],
      metadata: json['metadata'],
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'customer_phone': customerPhone,
      'customer_address': customerAddress,
      'store_id': storeId,
      'items': items.map((item) => item.toJson()).toList(),
      'subtotal': subtotal,
      'delivery_fee': deliveryFee,
      'discount': discount,
      'total': total,
      'status': status,
      'payment_method': paymentMethod,
      'payment_status': paymentStatus,
      'created_at': createdAt.toIso8601String(),
      'accepted_at': acceptedAt?.toIso8601String(),
      'ready_at': readyAt?.toIso8601String(),
      'delivered_at': deliveredAt?.toIso8601String(),
      'notes': notes,
      'cancel_reason': cancelReason,
      'metadata': metadata,
    };
  }

  // نسخ مع تعديل
  Order copyWith({
    String? id,
    String? customerId,
    String? customerName,
    String? customerPhone,
    String? customerAddress,
    String? storeId,
    List<OrderItem>? items,
    double? subtotal,
    double? deliveryFee,
    double? discount,
    double? total,
    String? status,
    String? paymentMethod,
    String? paymentStatus,
    DateTime? createdAt,
    DateTime? acceptedAt,
    DateTime? readyAt,
    DateTime? deliveredAt,
    String? notes,
    String? cancelReason,
    Map<String, dynamic>? metadata,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerAddress: customerAddress ?? this.customerAddress,
      storeId: storeId ?? this.storeId,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      discount: discount ?? this.discount,
      total: total ?? this.total,
      status: status ?? this.status,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      createdAt: createdAt ?? this.createdAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      readyAt: readyAt ?? this.readyAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      notes: notes ?? this.notes,
      cancelReason: cancelReason ?? this.cancelReason,
      metadata: metadata ?? this.metadata,
    );
  }

  // الحصول على نص الحالة
  String get statusText {
    switch (status) {
      case 'new':
        return 'طلب جديد';
      case 'accepted':
        return 'تم القبول';
      case 'preparing':
        return 'قيد التحضير';
      case 'ready':
        return 'جاهز للتسليم';
      case 'delivered':
        return 'تم التسليم';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }

  // الحصول على لون الحالة
  String get statusColor {
    switch (status) {
      case 'new':
        return '#3498DB';
      case 'accepted':
        return '#F39C12';
      case 'preparing':
        return '#E67E22';
      case 'ready':
        return '#27AE60';
      case 'delivered':
        return '#95A5A6';
      case 'cancelled':
        return '#E74C3C';
      default:
        return '#BDC3C7';
    }
  }

  // التحقق من إمكانية قبول الطلب
  bool get canAccept => status == 'new';

  // التحقق من إمكانية إلغاء الطلب
  bool get canCancel => ['new', 'accepted', 'preparing'].contains(status);

  // التحقق من إمكانية تحديد الطلب كجاهز
  bool get canMarkReady => ['accepted', 'preparing'].contains(status);

  // التحقق من إمكانية تحديد الطلب كمسلم
  bool get canMarkDelivered => status == 'ready';

  // حساب الوقت المنقضي منذ إنشاء الطلب
  Duration get timeSinceCreated => DateTime.now().difference(createdAt);

  // تنسيق الوقت المنقضي
  String get formattedTimeSinceCreated {
    final duration = timeSinceCreated;
    if (duration.inDays > 0) {
      return '${duration.inDays} يوم';
    } else if (duration.inHours > 0) {
      return '${duration.inHours} ساعة';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  @override
  String toString() {
    return 'Order{id: $id, customerName: $customerName, status: $status, total: $total}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Order &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

class OrderItem {
  final String productId;
  final String productName;
  final String? productImage;
  final double price;
  final int quantity;
  final double total;
  final String? notes;
  final List<String>? customizations;

  OrderItem({
    required this.productId,
    required this.productName,
    this.productImage,
    required this.price,
    required this.quantity,
    required this.total,
    this.notes,
    this.customizations,
  });

  factory OrderItem.fromJson(Map<String, dynamic> json) {
    return OrderItem(
      productId: json['product_id'] ?? '',
      productName: json['product_name'] ?? '',
      productImage: json['product_image'],
      price: (json['price'] ?? 0.0).toDouble(),
      quantity: json['quantity'] ?? 1,
      total: (json['total'] ?? 0.0).toDouble(),
      notes: json['notes'],
      customizations: json['customizations'] != null 
          ? List<String>.from(json['customizations']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'product_id': productId,
      'product_name': productName,
      'product_image': productImage,
      'price': price,
      'quantity': quantity,
      'total': total,
      'notes': notes,
      'customizations': customizations,
    };
  }
}
